nohup: ignoring input
INFO:__main__:API配置模块加载成功
INFO:__main__:支付服务模块加载成功
INFO:cache_manager:Redis缓存连接成功
INFO:db_optimizer:数据库连接池创建成功: 2-20 连接
INFO:__main__:性能优化模块加载成功
INFO:database_manager:PostgreSQL连接成功
INFO:database_manager:Redis连接成功
INFO:__main__:数据库管理器模块加载成功
INFO:__main__:蓝图注册成功
INFO:__main__:Redis连接成功
INFO:__main__:PostgreSQL连接成功
INFO:__main__:实时数据服务已集成统一API
INFO:services.trading_service:模拟交易环境初始化完成
INFO:__main__:实盘交易服务模块导入成功
INFO:__main__:报表服务模块导入成功
INFO:__main__:AI服务模块导入成功
INFO:__main__:股票预测服务模块导入成功
Traceback (most recent call last):
  File "/www/wwwroot/www.gdpp.com/app.py", line 5252, in <module>
    def stock_prediction():
  File "/www/wwwroot/www.gdpp.com/venv/lib/python3.10/site-packages/flask/scaffold.py", line 435, in decorator
    self.add_url_rule(rule, endpoint, f, **options)
  File "/www/wwwroot/www.gdpp.com/venv/lib/python3.10/site-packages/flask/scaffold.py", line 50, in wrapper_func
    return f(self, *args, **kwargs)
  File "/www/wwwroot/www.gdpp.com/venv/lib/python3.10/site-packages/flask/app.py", line 1059, in add_url_rule
    raise AssertionError(
AssertionError: View function mapping is overwriting an existing endpoint function: stock_prediction
