// 股票预测页面JavaScript

class StockPredictionManager {
    constructor() {
        this.currentMarket = 'us';
        this.selectedAnalysts = [];
        this.supportedAnalysts = {};
        this.userTier = 'basic'; // basic, vip, silver, gold, diamond
        this.dailyUsage = 0;
        this.dailyLimit = 3;
        this.tierLimits = {
            basic: { daily: 3, analysts: 3, markets: ['us'], models: ['basic'] },
            vip: { daily: 10, analysts: 8, markets: ['us', 'cn'], models: ['basic', 'gpt-4o'] },
            silver: { daily: 30, analysts: 12, markets: ['us', 'cn', 'hk'], models: ['basic', 'gpt-4o', 'claude-3'] },
            gold: { daily: 100, analysts: 16, markets: ['us', 'cn', 'hk', 'crypto'], models: ['basic', 'gpt-4o', 'claude-3', 'gemini'] },
            diamond: { daily: -1, analysts: 20, markets: ['us', 'cn', 'hk', 'crypto'], models: ['basic', 'gpt-4o', 'claude-3', 'gemini', 'custom'] }
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSupportedAnalysts();
        this.loadUserTier();
        this.updateAnalystChips();
        this.updateUIBasedOnTier();
        this.loadDailyUsage();
    }

    setupEventListeners() {
        // 市场选择
        document.querySelectorAll('.market-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchMarket(e.target.dataset.market);
            });
        });

        // 分析师选择
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('analyst-chip')) {
                this.toggleAnalyst(e.target.dataset.analyst);
            }
        });
    }

    async loadSupportedAnalysts() {
        try {
            const response = await fetch('/api/stock-prediction/analysts');
            const data = await response.json();

            if (data.success) {
                this.supportedAnalysts = data.analysts;
            }
        } catch (error) {
            console.error('加载分析师列表失败:', error);
            // 使用默认分析师列表
            this.supportedAnalysts = {
                'us': ['warren_buffett', 'ben_graham', 'peter_lynch', 'cathie_wood', 'bill_ackman'],
                'cn': ['ben_graham', 'peter_lynch', 'warren_buffett'],
                'hk': ['warren_buffett', 'ben_graham', 'peter_lynch'],
                'crypto': ['cathie_wood', 'stanley_druckenmiller', 'michael_burry']
            };
        }
    }

    switchMarket(market) {
        // 更新UI
        document.querySelectorAll('.market-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-market="${market}"]`).classList.add('active');

        this.currentMarket = market;
        this.selectedAnalysts = [];
        this.updateAnalystChips();
        this.updatePlaceholder();
    }

    updateAnalystChips() {
        const container = document.getElementById('analystChips');
        const analysts = this.supportedAnalysts[this.currentMarket] || [];

        container.innerHTML = analysts.map(analyst => {
            const displayName = this.getAnalystDisplayName(analyst);
            return `
                <span class="analyst-chip" data-analyst="${analyst}">
                    <i class="fas fa-user-tie"></i> ${displayName}
                </span>
            `;
        }).join('');
    }

    updatePlaceholder() {
        const input = document.getElementById('symbolInput');
        const placeholders = {
            'us': '输入美股代码，如: AAPL,TSLA,GOOGL',
            'cn': '输入A股代码，如: 000001,000002,600036',
            'hk': '输入港股代码，如: 0700,0941,1810',
            'crypto': '输入加密货币，如: BTCUSDT,ETHUSDT,BNBUSDT'
        };

        input.placeholder = placeholders[this.currentMarket];
        input.value = '';
    }

    toggleAnalyst(analyst) {
        const chip = document.querySelector(`[data-analyst="${analyst}"]`);

        if (this.selectedAnalysts.includes(analyst)) {
            // 取消选择
            this.selectedAnalysts = this.selectedAnalysts.filter(a => a !== analyst);
            chip.classList.remove('selected');
        } else {
            // 选择
            this.selectedAnalysts.push(analyst);
            chip.classList.add('selected');
        }
    }

    getAnalystDisplayName(analyst) {
        const names = {
            'warren_buffett': '沃伦·巴菲特',
            'ben_graham': '本杰明·格雷厄姆',
            'peter_lynch': '彼得·林奇',
            'cathie_wood': '凯茜·伍德',
            'bill_ackman': '比尔·阿克曼',
            'charlie_munger': '查理·芒格',
            'michael_burry': '迈克尔·伯里',
            'aswath_damodaran': '阿斯沃斯·达摩达兰',
            'phil_fisher': '菲利普·费雪',
            'stanley_druckenmiller': '斯坦利·德鲁肯米勒'
        };

        return names[analyst] || analyst;
    }

    async startPrediction() {
        const symbols = document.getElementById('symbolInput').value.trim();
        const model = document.getElementById('modelSelect').value;

        if (!symbols) {
            this.showAlert('请输入股票代码', 'warning');
            return;
        }

        if (this.selectedAnalysts.length === 0) {
            this.showAlert('请至少选择一位分析师', 'warning');
            return;
        }

        // 显示加载状态
        this.showLoading(true);
        this.hideResults();

        try {
            const response = await fetch('/api/stock-prediction/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    symbols: symbols.split(',').map(s => s.trim().toUpperCase()),
                    analysts: this.selectedAnalysts,
                    market_region: this.currentMarket,
                    model_name: model,
                    include_reasoning: true
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
            } else {
                this.showAlert(data.error || '预测失败', 'danger');
            }

        } catch (error) {
            console.error('预测请求失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }

    hideResults() {
        document.getElementById('consensusResults').style.display = 'none';
        document.getElementById('predictionResults').style.display = 'none';
    }

    displayResults(data) {
        this.displayConsensus(data.consensus);
        this.displayPredictions(data.predictions);

        // 显示结果
        document.getElementById('consensusResults').style.display = 'block';
        document.getElementById('predictionResults').style.display = 'block';
    }

    displayConsensus(consensus) {
        const container = document.getElementById('consensusResults');

        container.innerHTML = consensus.map(cons => `
            <div class="consensus-section">
                <h4><i class="fas fa-chart-line"></i> ${cons.symbol} - 共识预测</h4>

                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="signal-badge signal-${cons.consensus_signal} me-3">
                                ${this.getSignalText(cons.consensus_signal)}
                            </span>
                            <div>
                                <div>置信度: ${cons.consensus_confidence.toFixed(1)}%</div>
                                <small>基于 ${cons.analyst_count} 位分析师意见</small>
                            </div>
                        </div>

                        ${cons.avg_target_price ? `
                            <div class="mb-2">
                                <strong>平均目标价: $${cons.avg_target_price.toFixed(2)}</strong>
                            </div>
                        ` : ''}
                    </div>

                    <div class="col-md-6">
                        <div class="consensus-stats">
                            <div class="stat-item">
                                <div class="stat-value text-success">${cons.bullish_count}</div>
                                <div class="stat-label">看涨</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-danger">${cons.bearish_count}</div>
                                <div class="stat-label">看跌</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-warning">${cons.neutral_count}</div>
                                <div class="stat-label">中性</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    displayPredictions(predictions) {
        const container = document.getElementById('predictionResults');

        // 按股票分组
        const groupedPredictions = {};
        predictions.forEach(pred => {
            if (!groupedPredictions[pred.symbol]) {
                groupedPredictions[pred.symbol] = [];
            }
            groupedPredictions[pred.symbol].push(pred);
        });

        container.innerHTML = Object.entries(groupedPredictions).map(([symbol, preds]) => `
            <div class="prediction-card">
                <h4><i class="fas fa-chart-bar"></i> ${symbol} - 详细分析</h4>

                <div class="row">
                    ${preds.map(pred => `
                        <div class="col-md-6 mb-3">
                            <div class="analyst-card ${pred.signal}">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-user-tie"></i>
                                            ${this.getAnalystDisplayName(pred.analyst_name)}
                                        </h6>
                                        <span class="signal-badge signal-${pred.signal}">
                                            ${this.getSignalText(pred.signal)}
                                        </span>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">${pred.confidence.toFixed(1)}%</div>
                                        <small class="text-muted">置信度</small>
                                    </div>
                                </div>

                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: ${pred.confidence}%"></div>
                                </div>

                                ${pred.target_price ? `
                                    <div class="mt-2">
                                        <small><strong>目标价:</strong> $${pred.target_price.toFixed(2)}</small>
                                    </div>
                                ` : ''}

                                ${pred.reasoning ? `
                                    <div class="reasoning-text">
                                        <small><strong>分析理由:</strong></small><br>
                                        <small>${pred.reasoning}</small>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    getSignalText(signal) {
        const texts = {
            'bullish': '看涨',
            'bearish': '看跌',
            'neutral': '中性'
        };
        return texts[signal] || signal;
    }

    showAlert(message, type = 'info') {
        // 创建alert元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.position = 'fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.style.minWidth = '300px';

        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    // 商业化功能方法
    async loadUserTier() {
        try {
            const response = await fetch('/auth/status');
            const data = await response.json();

            if (data.success && data.user) {
                this.userTier = this.getUserTierFromRole(data.user.role, data.user.is_premium);
                this.dailyLimit = this.tierLimits[this.userTier].daily;
            }
        } catch (error) {
            console.error('加载用户等级失败:', error);
        }
    }

    getUserTierFromRole(role, isPremium) {
        if (role === 'admin') return 'diamond';
        if (!isPremium) return 'basic';

        // 根据会员类型返回等级
        return 'vip'; // 默认VIP，可以根据实际会员系统调整
    }

    async loadDailyUsage() {
        try {
            const response = await fetch('/api/stock-prediction/usage');
            const data = await response.json();

            if (data.success) {
                this.dailyUsage = data.daily_usage || 0;
                this.updateUsageDisplay();
            }
        } catch (error) {
            console.error('加载使用次数失败:', error);
        }
    }

    updateUsageDisplay() {
        const usageInfo = document.getElementById('usageInfo');
        const usedCount = document.getElementById('usedCount');
        const totalCount = document.getElementById('totalCount');

        if (this.dailyLimit > 0) {
            usageInfo.style.display = 'block';
            usedCount.textContent = this.dailyUsage;
            totalCount.textContent = this.dailyLimit;
        } else {
            usageInfo.style.display = 'none';
        }
    }

    updateUIBasedOnTier() {
        const currentLimits = this.tierLimits[this.userTier];

        // 更新市场选择限制
        document.querySelectorAll('.market-tab').forEach(tab => {
            const market = tab.dataset.market;
            if (!currentLimits.markets.includes(market)) {
                tab.classList.add('disabled');
                tab.style.opacity = '0.5';
                tab.style.pointerEvents = 'none';

                // 添加升级提示
                const badge = document.createElement('span');
                badge.className = 'premium-badge';
                badge.textContent = '需升级';
                badge.style.fontSize = '0.7rem';
                tab.appendChild(badge);
            }
        });

        // 更新AI模型选择限制
        const modelSelect = document.getElementById('modelSelect');
        Array.from(modelSelect.options).forEach(option => {
            const tier = option.dataset.tier;
            if (tier && !this.canAccessTier(tier)) {
                option.disabled = true;
                option.textContent += ' (需升级)';
            }
        });

        // 显示限制提示
        if (this.userTier === 'basic') {
            document.getElementById('symbolLimitBadge').style.display = 'inline-block';
            document.getElementById('modelLimitBadge').style.display = 'inline-block';
            document.getElementById('analystLimitBadge').style.display = 'inline-block';
        }
    }

    canAccessTier(requiredTier) {
        const tierOrder = ['basic', 'vip', 'silver', 'gold', 'diamond'];
        const userTierIndex = tierOrder.indexOf(this.userTier);
        const requiredTierIndex = tierOrder.indexOf(requiredTier);
        return userTierIndex >= requiredTierIndex;
    }

    checkUsageLimit() {
        if (this.dailyLimit > 0 && this.dailyUsage >= this.dailyLimit) {
            this.showUpgradePrompt('今日预测次数已用完', '升级会员获得更多预测次数');
            return false;
        }
        return true;
    }

    showUpgradePrompt(title, message) {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning">
                        <h5 class="modal-title text-dark">
                            <i class="fas fa-crown me-2"></i>${title}
                        </h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p class="mb-4">${message}</p>
                        <div class="upgrade-cta">
                            <h4>升级VIP会员</h4>
                            <div class="price-tag">¥99/月</div>
                            <p>享受10倍预测次数 + 8位AI分析师 + 多市场支持</p>
                            <a href="/membership" class="btn btn-warning btn-lg">
                                <i class="fas fa-crown me-2"></i>立即升级
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
}

// 全局功能函数
function showFeaturesComparison() {
    document.getElementById('featureComparison').style.display = 'block';
    document.getElementById('predictionForm').style.display = 'none';
}

function hideFeaturesComparison() {
    document.getElementById('featureComparison').style.display = 'none';
    document.getElementById('predictionForm').style.display = 'block';
}

function showMembershipPlans() {
    window.location.href = '/membership';
}

// 全局函数
function startPrediction() {
    if (stockPredictionManager.checkUsageLimit()) {
        stockPredictionManager.startPrediction();
    }
}

// 初始化股票预测管理器
const stockPredictionManager = new StockPredictionManager();
