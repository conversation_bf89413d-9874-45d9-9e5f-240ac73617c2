<!-- 统一登录模态框组件 -->
<div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--glass-bg, rgba(30, 41, 59, 0.8)); backdrop-filter: blur(20px); border: 1px solid var(--glass-border, rgba(255,255,255,0.2));">
            <div class="modal-header border-bottom border-secondary">
                <h5 class="modal-title text-white">用户登录</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm" onsubmit="event.preventDefault(); performLogin();">
                    <div class="mb-3">
                        <label class="form-label text-white">用户名或邮箱</label>
                        <input type="text" class="form-control" id="loginUsername" required
                               placeholder="请输入用户名或邮箱"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">密码</label>
                        <input type="password" class="form-control" id="loginPassword" required
                               placeholder="请输入密码"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label text-white" for="rememberMe">记住我</label>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">登录</button>
                </form>
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        还没有账户？
                        <a href="#" onclick="showRegister()" class="text-primary">立即注册</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统一注册模态框组件 -->
<div class="modal fade" id="registerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--glass-bg, rgba(30, 41, 59, 0.8)); backdrop-filter: blur(20px); border: 1px solid var(--glass-border, rgba(255,255,255,0.2));">
            <div class="modal-header border-bottom border-secondary">
                <h5 class="modal-title text-white">用户注册</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="registerForm" onsubmit="event.preventDefault(); performRegister();">
                    <div class="mb-3">
                        <label class="form-label text-white">用户名</label>
                        <input type="text" class="form-control" id="registerUsername" required
                               placeholder="请输入用户名"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">邮箱</label>
                        <input type="email" class="form-control" id="registerEmail" required
                               placeholder="请输入邮箱地址"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">全名</label>
                        <input type="text" class="form-control" id="registerFullName"
                               placeholder="请输入真实姓名（可选）"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">密码</label>
                        <input type="password" class="form-control" id="registerPassword" required
                               placeholder="请输入密码"
                               style="background: rgba(15,23,42,0.8); border: 1px solid var(--glass-border, rgba(255,255,255,0.3)); color: white;">
                    </div>
                    <button type="submit" class="btn btn-primary w-100">注册</button>
                </form>
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        已有账户？
                        <a href="#" onclick="showLogin()" class="text-primary">立即登录</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 统一登录逻辑
async function performLogin() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe')?.checked || false;

    if (!username || !password) {
        showNotification('请填写完整的登录信息', 'error');
        return;
    }

    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,  // 后端会自动识别用户名或邮箱
                password: password,
                remember_me: rememberMe
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('登录成功！', 'success');

            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }

            // 动态更新页面用户状态，而不是刷新页面
            updateUserStatusAfterLogin(result.user);
        } else {
            // 智能错误提示
            if (result.error && result.error.includes('用户不存在') || result.error.includes('用户名/邮箱或密码错误')) {
                showSmartAuthPrompt('login', username, result.error);
            } else {
                showNotification(result.error || '登录失败', 'error');
            }
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        showNotification('网络错误，请稍后重试', 'error');
    }
}

// 登录成功后更新页面用户状态
function updateUserStatusAfterLogin(user) {
    try {
        // 更新导航栏用户信息
        updateNavbarUserInfo(user);

        // 更新页面中的登录/注册按钮
        updateLoginButtons(user);

        // 更新用户相关的页面内容
        updatePageContent(user);

        // 触发自定义事件，让其他组件知道用户已登录
        window.dispatchEvent(new CustomEvent('userLoggedIn', {
            detail: { user: user }
        }));

    } catch (error) {
        console.error('更新用户状态失败:', error);
        // 如果动态更新失败，回退到刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// 更新导航栏用户信息
function updateNavbarUserInfo(user) {
    // 查找并更新用户头像
    const avatarElements = document.querySelectorAll('.user-avatar, .avatar-img');
    avatarElements.forEach(el => {
        if (el.tagName === 'IMG') {
            el.src = user.avatar_url || '/static/img/avatar-default.png';
            el.alt = user.full_name || user.username;
        }
    });

    // 查找并更新用户名显示
    const usernameElements = document.querySelectorAll('.user-name, .username-display');
    usernameElements.forEach(el => {
        el.textContent = user.full_name || user.username;
    });

    // 查找并更新用户角色/会员状态
    const roleElements = document.querySelectorAll('.user-role, .member-status');
    roleElements.forEach(el => {
        if (user.is_premium) {
            el.textContent = 'VIP会员';
            el.className = el.className.replace(/\bbadge-\w+/g, '') + ' badge-warning';
        } else {
            el.textContent = '普通用户';
            el.className = el.className.replace(/\bbadge-\w+/g, '') + ' badge-secondary';
        }
    });
}

// 更新登录/注册按钮
function updateLoginButtons(user) {
    // 更新首页导航栏（如果存在）
    const navbarUser = document.getElementById('navbarUser');
    if (navbarUser) {
        navbarUser.innerHTML = `
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle me-1"></i>${user.full_name || user.username}
                    ${user.is_premium ? '<span class="badge bg-warning ms-1">VIP</span>' : ''}
                </a>
                <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                    <li><a class="dropdown-item text-white" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>仪表板</a></li>
                    <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                    <li><a class="dropdown-item text-white" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                    ${!user.is_premium ? '<li><a class="dropdown-item text-white" href="#" onclick="showUpgrade()"><i class="fas fa-crown me-2"></i>升级VIP</a></li>' : ''}
                    <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                    <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                </ul>
            </li>
        `;
    }

    // 隐藏其他页面的登录/注册按钮
    const loginButtons = document.querySelectorAll('.login-btn, .register-btn, .btn-login, .btn-register');
    loginButtons.forEach(btn => {
        btn.style.display = 'none';
    });

    // 显示其他页面的用户菜单
    const userMenus = document.querySelectorAll('.user-menu, .user-dropdown, .navbar-user');
    userMenus.forEach(menu => {
        if (menu.id !== 'navbarUser') { // 避免重复处理首页导航
            menu.style.display = 'block';
        }
    });

    // 更新欢迎信息
    const welcomeElements = document.querySelectorAll('.welcome-message, .user-welcome');
    welcomeElements.forEach(el => {
        el.textContent = `欢迎，${user.full_name || user.username}！`;
        el.style.display = 'block';
    });
}

// 更新页面内容
function updatePageContent(user) {
    // 更新会员专享内容的显示状态
    const premiumElements = document.querySelectorAll('.premium-only, .vip-content');
    premiumElements.forEach(el => {
        if (user.is_premium) {
            el.style.display = 'block';
            el.classList.remove('disabled');
        } else {
            el.style.display = 'none';
            el.classList.add('disabled');
        }
    });

    // 更新需要登录的内容
    const loginRequiredElements = document.querySelectorAll('.login-required, .auth-required');
    loginRequiredElements.forEach(el => {
        el.style.display = 'block';
        el.classList.remove('disabled');
    });

    // 更新用户相关的链接和按钮
    const userLinks = document.querySelectorAll('a[href*="dashboard"], a[href*="profile"], a[href*="my-"]');
    userLinks.forEach(link => {
        link.classList.remove('disabled');
        link.style.pointerEvents = 'auto';
    });
}

// 统一注册逻辑
async function performRegister() {
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const fullName = document.getElementById('registerFullName').value.trim();
    const password = document.getElementById('registerPassword').value;

    if (!username || !email || !password) {
        showNotification('请填写必填信息', 'error');
        return;
    }

    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('注册成功！', 'success');

            // 关闭模态框
            const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
            if (registerModal) {
                registerModal.hide();
            }

            // 动态更新页面用户状态，而不是跳转
            updateUserStatusAfterLogin(result.user);
        } else {
            // 智能错误提示
            if (result.error && (result.error.includes('用户已存在') || result.error.includes('用户名已被使用') || result.error.includes('邮箱已被注册'))) {
                showSmartAuthPrompt('register', username, result.error);
            } else {
                showNotification(result.error || '注册失败', 'error');
            }
        }
    } catch (error) {
        console.error('注册请求失败:', error);
        showNotification('网络错误，请稍后重试', 'error');
    }
}

// 显示登录模态框
function showLogin() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// 显示注册模态框
function showRegister() {
    // 先关闭登录模态框
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }

    // 显示注册模态框
    setTimeout(() => {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    }, 300);
}

// 智能认证提示函数
function showSmartAuthPrompt(type, username, errorMessage) {
    let title, message, actionText, actionFunction;

    if (type === 'login') {
        // 登录失败，用户不存在
        title = '用户不存在';
        message = `用户名/邮箱 "${username}" 不存在，是否要注册新账户？`;
        actionText = '立即注册';
        actionFunction = () => {
            // 关闭当前模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }

            // 预填用户名到注册表单
            setTimeout(() => {
                const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
                registerModal.show();

                // 预填用户名或邮箱
                const registerUsernameInput = document.getElementById('registerUsername');
                const registerEmailInput = document.getElementById('registerEmail');

                if (username.includes('@')) {
                    registerEmailInput.value = username;
                } else {
                    registerUsernameInput.value = username;
                }
            }, 300);
        };
    } else if (type === 'register') {
        // 注册失败，用户已存在
        title = '用户已存在';
        message = `用户名/邮箱 "${username}" 已被注册，是否要直接登录？`;
        actionText = '立即登录';
        actionFunction = () => {
            // 关闭当前模态框
            const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
            if (registerModal) {
                registerModal.hide();
            }

            // 预填用户名到登录表单
            setTimeout(() => {
                const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
                loginModal.show();

                // 预填用户名或邮箱
                const loginUsernameInput = document.getElementById('loginUsername');
                loginUsernameInput.value = username;

                // 聚焦到密码输入框
                setTimeout(() => {
                    document.getElementById('loginPassword').focus();
                }, 100);
            }, 300);
        };
    }

    // 创建智能提示模态框
    const smartPromptModal = document.createElement('div');
    smartPromptModal.className = 'modal fade';
    smartPromptModal.id = 'smartAuthPrompt';
    smartPromptModal.innerHTML = `
        <div class="modal-dialog modal-sm">
            <div class="modal-content" style="background: var(--glass-bg, rgba(30, 41, 59, 0.9)); backdrop-filter: blur(20px); border: 1px solid var(--glass-border, rgba(255,255,255,0.2));">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-info-circle text-warning me-2"></i>${title}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <p class="text-white mb-4">${message}</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-warning" onclick="smartAuthAction()">
                            <i class="fas fa-arrow-right me-2"></i>${actionText}
                        </button>
                        <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(smartPromptModal);

    // 设置全局动作函数
    window.smartAuthAction = () => {
        const modal = bootstrap.Modal.getInstance(smartPromptModal);
        if (modal) {
            modal.hide();
        }
        actionFunction();

        // 清理
        setTimeout(() => {
            if (smartPromptModal.parentNode) {
                smartPromptModal.remove();
            }
            delete window.smartAuthAction;
        }, 500);
    };

    // 显示模态框
    const modal = new bootstrap.Modal(smartPromptModal);
    modal.show();

    // 模态框关闭时清理
    smartPromptModal.addEventListener('hidden.bs.modal', () => {
        setTimeout(() => {
            if (smartPromptModal.parentNode) {
                smartPromptModal.remove();
            }
            delete window.smartAuthAction;
        }, 100);
    });
}

// 通知函数（如果页面没有定义的话）
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}
</script>
