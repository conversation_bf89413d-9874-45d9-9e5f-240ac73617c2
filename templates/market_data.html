<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时行情 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            z-index: 1050;
            position: relative;
        }

        /* 修复下拉菜单层级问题 */
        .dropdown-menu {
            z-index: 1060 !important;
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
        }

        .dropdown-item {
            color: var(--text-primary) !important;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            color: var(--primary) !important;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary) !important;
        }

        .main-container {
            padding: 20px;
        }

        .data-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .market-overview {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid var(--glass-border);
        }

        .index-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }

        .index-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .index-change {
            font-size: 1rem;
            margin-bottom: 5px;
        }

        .index-name {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stock-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: transform 0.2s;
        }

        .stock-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .stock-item.negative {
            border-left-color: #dc3545;
        }

        .stock-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stock-symbol {
            font-weight: bold;
            font-size: 1.2rem;
        }

        .stock-price {
            font-weight: bold;
            font-size: 1.3rem;
        }

        .stock-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-weight: bold;
        }

        .price-up {
            color: #28a745;
        }

        .price-down {
            color: #dc3545;
        }

        .price-neutral {
            color: #6c757d;
        }

        .market-tabs {
            margin-bottom: 20px;
        }

        .market-tab {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            margin-right: 10px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .market-tab.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 10px;
            padding: 12px 15px;
            width: 100%;
        }

        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            color: white;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
            display: none;
        }

        .auto-refresh-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 10px;
        }

        .auto-refresh-toggle.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        /* 新增样式：市场概览美化 */
        .index-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .index-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .index-item.crypto {
            border-color: rgba(255, 193, 7, 0.3);
            background: rgba(255, 193, 7, 0.05);
        }

        .index-item.commodity {
            border-color: rgba(255, 152, 0, 0.3);
            background: rgba(255, 152, 0, 0.05);
        }

        .index-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .index-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .index-change {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .index-name {
            font-size: 0.85rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .market-status {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .status-indicator i {
            font-size: 0.7rem;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }

            .data-card {
                padding: 15px;
            }

            .stock-details {
                grid-template-columns: repeat(2, 1fr);
            }

            .market-tab {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .index-item {
                padding: 15px;
                margin-bottom: 15px;
            }

            .index-value {
                font-size: 1.2rem;
            }

            .index-icon {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item" data-feature="stock-prediction" style="display: none;">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/market-data">
                            <i class="fas fa-chart-area me-1"></i>实时行情
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>账户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/membership">会员中心</a></li>
                            <li><a class="dropdown-item" href="/security">安全设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 刷新指示器 -->
    <div class="refresh-indicator" id="refreshIndicator">
        <i class="fas fa-sync-alt fa-spin"></i> 数据更新中...
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="data-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-chart-line"></i> 实时行情数据</h2>
                    <p class="text-muted mb-0">基于akshare和东方财富的实时金融数据</p>
                </div>
                <div>
                    <a href="/dashboard" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left"></i> 返回仪表板
                    </a>
                    <button class="auto-refresh-toggle" id="autoRefreshToggle" onclick="toggleAutoRefresh()">
                        <i class="fas fa-sync-alt"></i> 自动刷新
                    </button>
                </div>
            </div>

            <!-- 市场选择 -->
            <div class="market-tabs">
                <button class="market-tab active" data-market="cn" onclick="switchMarket('cn')">
                    <i class="fas fa-flag"></i> A股市场
                </button>
                <button class="market-tab" data-market="us" onclick="switchMarket('us')">
                    <i class="fas fa-flag-usa"></i> 美股市场
                </button>
                <button class="market-tab" data-market="hk" onclick="switchMarket('hk')">
                    <i class="fas fa-building"></i> 港股市场
                </button>
                <button class="market-tab" data-market="crypto" onclick="switchMarket('crypto')">
                    <i class="fab fa-bitcoin"></i> 加密货币
                </button>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <input type="text" class="search-input" id="symbolInput"
                               placeholder="输入股票代码，如: 000001,000002,600036">
                        <small class="text-white-50 d-block mt-1">多个代码用逗号分隔</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn-search w-100" onclick="searchStocks()">
                            <i class="fas fa-search"></i> 查询行情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场概览 -->
        <div class="market-overview" id="marketOverview">
            <h4><i class="fas fa-globe"></i> 全球金融市场概览</h4>
            <div class="row" id="indexList">
                <!-- 美股指数 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="index-value">4,756.50</div>
                        <div class="index-change text-success">+12.34 (+0.26%)</div>
                        <div class="index-name">标普500</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-microchip"></i></div>
                        <div class="index-value">14,845.73</div>
                        <div class="index-change text-danger">-23.45 (-0.16%)</div>
                        <div class="index-name">纳斯达克</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-industry"></i></div>
                        <div class="index-value">37,863.80</div>
                        <div class="index-change text-success">+45.67 (+0.12%)</div>
                        <div class="index-name">道琼斯</div>
                    </div>
                </div>

                <!-- 亚太指数 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-yen-sign"></i></div>
                        <div class="index-value">2,974.93</div>
                        <div class="index-change text-danger">-8.12 (-0.27%)</div>
                        <div class="index-name">上证指数</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-building"></i></div>
                        <div class="index-value">16,077.36</div>
                        <div class="index-change text-success">+156.78 (+0.98%)</div>
                        <div class="index-name">恒生指数</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-torii-gate"></i></div>
                        <div class="index-value">35,909.70</div>
                        <div class="index-change text-danger">-89.23 (-0.25%)</div>
                        <div class="index-name">日经225</div>
                    </div>
                </div>

                <!-- 欧洲指数 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-pound-sign"></i></div>
                        <div class="index-value">7,630.05</div>
                        <div class="index-change text-success">+23.45 (+0.31%)</div>
                        <div class="index-name">富时100</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item">
                        <div class="index-icon"><i class="fas fa-euro-sign"></i></div>
                        <div class="index-value">16,735.02</div>
                        <div class="index-change text-danger">-34.56 (-0.21%)</div>
                        <div class="index-name">德国DAX</div>
                    </div>
                </div>

                <!-- 数字货币 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item crypto">
                        <div class="index-icon"><i class="fab fa-bitcoin"></i></div>
                        <div class="index-value">$42,350.67</div>
                        <div class="index-change text-warning">+1,234.56 (+3.01%)</div>
                        <div class="index-name">比特币</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item crypto">
                        <div class="index-icon"><i class="fab fa-ethereum"></i></div>
                        <div class="index-value">$2,567.89</div>
                        <div class="index-change text-danger">-45.67 (-1.75%)</div>
                        <div class="index-name">以太坊</div>
                    </div>
                </div>

                <!-- 大宗商品 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item commodity">
                        <div class="index-icon"><i class="fas fa-coins"></i></div>
                        <div class="index-value">$2,034.50</div>
                        <div class="index-change text-warning">+12.30 (+0.61%)</div>
                        <div class="index-name">黄金</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="index-item commodity">
                        <div class="index-icon"><i class="fas fa-oil-can"></i></div>
                        <div class="index-value">$78.45</div>
                        <div class="index-change text-danger">-1.23 (-1.54%)</div>
                        <div class="index-name">原油</div>
                    </div>
                </div>
            </div>

            <!-- 市场状态指示器 -->
            <div class="market-status mt-4">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="status-indicator">
                            <i class="fas fa-circle text-success"></i>
                            <span>美股开盘</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-indicator">
                            <i class="fas fa-circle text-warning"></i>
                            <span>亚太休市</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-indicator">
                            <i class="fas fa-circle text-danger"></i>
                            <span>欧洲休市</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="status-indicator">
                            <i class="fas fa-circle text-success"></i>
                            <span>数字货币24H</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-white" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-white mt-3">正在获取实时数据...</p>
        </div>

        <!-- 股票行情列表 -->
        <div class="data-card" id="stockDataContainer" style="display: none;">
            <h4><i class="fas fa-list"></i> 实时行情</h4>
            <div id="stockList">
                <!-- 动态生成股票数据 -->
            </div>
        </div>

        <!-- 数据源信息 -->
        <div class="data-card">
            <h5><i class="fas fa-database"></i> 数据源信息</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>国内数据源</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> akshare - A股实时行情</li>
                        <li><i class="fas fa-check text-success"></i> 东方财富 - 市场数据</li>
                        <li><i class="fas fa-check text-success"></i> 新浪财经 - 港股数据</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>国外数据源</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Yahoo Finance - 美股行情</li>
                        <li><i class="fas fa-check text-success"></i> Alpha Vantage - 金融数据</li>
                        <li><i class="fas fa-check text-success"></i> Binance API - 加密货币</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/market_data.js') }}"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户登录状态和会员权限
            checkLoginStatus();
        });

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    window.location.href = '/';
                }
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }
    </script>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap mb-3">
                        <a href="/realtime" class="text-secondary text-decoration-none">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                        <a href="/market-data" class="text-secondary text-decoration-none">
                            <i class="fas fa-chart-area me-1"></i>实时行情
                        </a>
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="/api-docs" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="/contact" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="/privacy" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                        <a href="/terms" class="text-secondary text-decoration-none">
                            <i class="fas fa-file-contract me-1"></i>服务条款
                        </a>
                        <a href="/help" class="text-secondary text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>帮助中心
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
</body>
</html>
