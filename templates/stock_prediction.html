<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI股票预测 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px;
        }

        .prediction-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .analyst-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }

        .analyst-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .analyst-card.bullish {
            border-left-color: #28a745;
        }

        .analyst-card.bearish {
            border-left-color: #dc3545;
        }

        .analyst-card.neutral {
            border-left-color: #ffc107;
        }

        .signal-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .signal-bullish {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .signal-bearish {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .signal-neutral {
            background: linear-gradient(135deg, #ffc107, #ffca2c);
            color: #212529;
        }

        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .consensus-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .market-tabs {
            margin-bottom: 20px;
        }

        .market-tab {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .market-tab.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        .analyst-selector {
            margin-bottom: 20px;
        }

        .analyst-chip {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .analyst-chip.selected {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border-color: #667eea;
        }

        .prediction-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-control-custom {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 10px;
            padding: 12px 15px;
        }

        .btn-predict {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .btn-predict:hover {
            transform: translateY(-2px);
            color: white;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .reasoning-text {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .consensus-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* 商业化功能样式 */
        .premium-feature {
            position: relative;
            overflow: hidden;
        }

        .premium-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 165, 0, 0.9));
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 15px;
        }

        .premium-badge {
            background: linear-gradient(135deg, #ffd700, #ffb347);
            color: #000;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .membership-tier {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            margin-left: 5px;
        }

        .tier-basic { background: #6c757d; color: white; }
        .tier-vip { background: #ffc107; color: #000; }
        .tier-silver { background: #c0c0c0; color: #000; }
        .tier-gold { background: #ffd700; color: #000; }
        .tier-diamond { background: linear-gradient(135deg, #b9f2ff, #0891b2); color: #000; }

        .feature-comparison {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .feature-check {
            color: #28a745;
            font-size: 1.2rem;
        }

        .feature-cross {
            color: #dc3545;
            font-size: 1.2rem;
        }

        .upgrade-cta {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            color: white;
            margin: 20px 0;
        }

        .price-tag {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            margin: 10px 0;
        }

        .limited-results {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .limited-results::after {
            content: "升级VIP解锁完整功能";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: #ffd700;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }

            .prediction-card {
                padding: 20px;
            }

            .consensus-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="prediction-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-brain"></i> AI股票预测</h2>
                    <p class="text-muted mb-0">汇聚投资大师智慧，AI驱动的股票分析预测</p>
                </div>
                <a href="/dashboard" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> 返回仪表板
                </a>
            </div>

            <!-- 市场选择 -->
            <div class="market-tabs">
                <button class="market-tab active" data-market="us">
                    <i class="fas fa-flag-usa"></i> 美股
                </button>
                <button class="market-tab" data-market="cn">
                    <i class="fas fa-flag"></i> A股
                </button>
                <button class="market-tab" data-market="hk">
                    <i class="fas fa-building"></i> 港股
                </button>
                <button class="market-tab" data-market="crypto">
                    <i class="fab fa-bitcoin"></i> 加密货币
                </button>
            </div>

            <!-- 会员功能对比 -->
            <div class="feature-comparison" id="featureComparison" style="display: none;">
                <h4 class="text-center text-dark mb-4">
                    <i class="fas fa-crown text-warning"></i> AI股票预测功能对比
                </h4>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能特性</th>
                            <th>基础用户</th>
                            <th>VIP会员 <span class="tier-vip">VIP</span></th>
                            <th>白银会员 <span class="tier-silver">白银</span></th>
                            <th>黄金会员 <span class="tier-gold">黄金</span></th>
                            <th>钻石会员 <span class="tier-diamond">钻石</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>每日预测次数</td>
                            <td>3次</td>
                            <td>10次</td>
                            <td>30次</td>
                            <td>100次</td>
                            <td>无限制</td>
                        </tr>
                        <tr>
                            <td>AI分析师数量</td>
                            <td>3位</td>
                            <td>8位</td>
                            <td>12位</td>
                            <td>16位</td>
                            <td>16位+定制</td>
                        </tr>
                        <tr>
                            <td>支持市场</td>
                            <td>美股</td>
                            <td>美股+A股</td>
                            <td>美股+A股+港股</td>
                            <td>全市场</td>
                            <td>全市场+期货</td>
                        </tr>
                        <tr>
                            <td>AI模型</td>
                            <td>基础模型</td>
                            <td>GPT-4o</td>
                            <td>GPT-4o+Claude</td>
                            <td>全部模型</td>
                            <td>全部+定制</td>
                        </tr>
                        <tr>
                            <td>详细分析报告</td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                        </tr>
                        <tr>
                            <td>实时市场情绪</td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                        </tr>
                        <tr>
                            <td>风险评估报告</td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                        </tr>
                        <tr>
                            <td>个性化推荐</td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-times feature-cross"></i></td>
                            <td><i class="fas fa-check feature-check"></i></td>
                        </tr>
                    </tbody>
                </table>

                <div class="text-center mt-4">
                    <button class="btn btn-warning btn-lg" onclick="showMembershipPlans()">
                        <i class="fas fa-crown me-2"></i>查看会员套餐
                    </button>
                    <button class="btn btn-outline-primary btn-lg ms-2" onclick="hideFeaturesComparison()">
                        <i class="fas fa-times me-2"></i>关闭对比
                    </button>
                </div>
            </div>

            <!-- 预测表单 -->
            <div class="prediction-form" id="predictionForm">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-white">
                            股票代码
                            <span class="premium-badge" id="symbolLimitBadge" style="display: none;">
                                限制功能
                            </span>
                        </label>
                        <input type="text" class="form-control form-control-custom" id="symbolInput"
                               placeholder="输入股票代码，如: AAPL,TSLA,GOOGL">
                        <small class="text-white-50" id="symbolHint">多个代码用逗号分隔</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-white">
                            AI模型
                            <span class="premium-badge" id="modelLimitBadge" style="display: none;">
                                VIP专享
                            </span>
                        </label>
                        <select class="form-control form-control-custom" id="modelSelect">
                            <option value="basic">基础模型 (免费)</option>
                            <option value="gpt-4o" data-tier="vip">GPT-4o (VIP)</option>
                            <option value="claude-3" data-tier="silver">Claude 3 (白银)</option>
                            <option value="gemini" data-tier="gold">Gemini Pro (黄金)</option>
                            <option value="custom" data-tier="diamond">定制模型 (钻石)</option>
                        </select>
                    </div>
                </div>

                <!-- 分析师选择 -->
                <div class="analyst-selector">
                    <label class="form-label text-white">
                        选择分析师 (点击选择/取消)
                        <span class="premium-badge" id="analystLimitBadge" style="display: none;">
                            部分限制
                        </span>
                    </label>
                    <div id="analystChips">
                        <!-- 动态生成分析师选项 -->
                    </div>
                    <small class="text-white-50" id="analystHint">基础用户可选择3位分析师</small>
                </div>

                <div class="text-center">
                    <button class="btn btn-predict" onclick="startPrediction()">
                        <i class="fas fa-magic"></i> 开始AI预测
                    </button>
                    <button class="btn btn-outline-light ms-2" onclick="showFeaturesComparison()">
                        <i class="fas fa-info-circle"></i> 功能对比
                    </button>
                </div>

                <!-- 使用次数提示 -->
                <div class="mt-3 text-center" id="usageInfo" style="display: none;">
                    <small class="text-white-50">
                        今日已使用 <span id="usedCount">0</span> / <span id="totalCount">3</span> 次预测
                        <a href="/membership" class="text-warning ms-2">升级获得更多次数</a>
                    </small>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-white" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="text-white mt-3">AI大师们正在分析中，请稍候...</p>
        </div>

        <!-- 共识预测结果 -->
        <div id="consensusResults" style="display: none;">
            <!-- 动态生成共识结果 -->
        </div>

        <!-- 详细预测结果 -->
        <div id="predictionResults" style="display: none;">
            <!-- 动态生成预测结果 -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/stock_prediction.js') }}"></script>

    <script>
        // 页面加载时检查会员权限
        document.addEventListener('DOMContentLoaded', function() {
            checkStockPredictionPermission();
        });

        async function checkStockPredictionPermission() {
            try {
                const response = await fetch('/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.authenticated && data.user) {
                        const user = data.user;
                        if (hasPermission(user, 'stock-prediction')) {
                            // 用户有权限，显示功能
                            document.querySelector('.main-container').style.display = 'block';
                        } else {
                            // 用户无权限，显示升级提示
                            showUpgradePrompt();
                        }
                    } else {
                        // 用户未登录，跳转到首页
                        window.location.href = '/?redirect=stock-prediction';
                    }
                } else {
                    // 检查失败，跳转到首页
                    window.location.href = '/?redirect=stock-prediction';
                }
            } catch (error) {
                console.error('权限检查失败:', error);
                window.location.href = '/?redirect=stock-prediction';
            }
        }

        function showUpgradePrompt() {
            document.querySelector('.main-container').innerHTML = `
                <div class="prediction-card text-center">
                    <div class="mb-4">
                        <i class="fas fa-crown" style="font-size: 4rem; color: #ffd700;"></i>
                    </div>
                    <h2 class="mb-3">AI股票预测 - VIP专享功能</h2>
                    <p class="text-muted mb-4">
                        AI股票预测功能需要VIP会员权限才能使用。<br>
                        升级到VIP会员，享受AI驱动的专业股票分析预测服务。
                    </p>

                    <div class="row justify-content-center mb-4">
                        <div class="col-md-8">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">🌟 VIP会员特权</h5>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-success me-2"></i>AI股票预测分析</li>
                                        <li><i class="fas fa-check text-success me-2"></i>多模型智能预测</li>
                                        <li><i class="fas fa-check text-success me-2"></i>实时市场数据</li>
                                        <li><i class="fas fa-check text-success me-2"></i>专业投资建议</li>
                                        <li><i class="fas fa-check text-success me-2"></i>风险评估报告</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center gap-3">
                        <a href="/membership" class="btn btn-warning btn-lg">
                            <i class="fas fa-crown me-2"></i>立即升级VIP
                        </a>
                        <a href="/dashboard" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>返回仪表板
                        </a>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
