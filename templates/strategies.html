<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略市场 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --dark-surface: #1e293b;
            --dark-card: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, #1a202c 50%, var(--dark-surface) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* 导航栏 */
        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--primary);
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary) !important;
            background: rgba(99, 102, 241, 0.1);
        }

        /* 卡片样式 */
        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .strategy-card {
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .strategy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .strategy-card:hover::before {
            transform: scaleX(1);
        }

        .strategy-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .rating {
            color: var(--warning);
        }

        .category-badge {
            background: rgba(99, 102, 241, 0.2);
            color: var(--primary);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            border: 1px solid rgba(99, 102, 241, 0.3);
        }

        .price-tag {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn-outline-light {
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary);
            color: var(--primary);
            transform: translateY(-2px);
        }

        /* 表单样式 */
        .search-box, .form-control, .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .search-box:focus, .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            color: var(--text-primary);
        }

        .search-box::placeholder, .form-control::placeholder {
            color: var(--text-muted);
        }

        .filter-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 页面标题 */
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 50%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        /* 模态框样式修复 */
        .modal-content {
            background: var(--dark-surface) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: 16px !important;
            color: var(--text-primary) !important;
            backdrop-filter: blur(20px) !important;
        }

        .modal-header {
            border-bottom: 1px solid var(--glass-border);
            background: rgba(99, 102, 241, 0.1);
        }

        .modal-title {
            color: var(--text-primary) !important;
            font-weight: 600;
        }

        .modal-body {
            color: var(--text-primary) !important;
        }

        .modal-body h6 {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .modal-body p, .modal-body li {
            color: var(--text-secondary);
        }

        .modal-body strong {
            color: var(--text-primary);
        }

        .modal-footer {
            border-top: 1px solid var(--glass-border);
            background: rgba(0, 0, 0, 0.1);
        }

        /* 代码块样式 */
        .modal-body pre {
            background: var(--dark) !important;
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 1rem;
            color: var(--text-primary) !important;
            max-height: 300px;
            overflow-y: auto;
        }

        .modal-body code {
            color: var(--text-primary) !important;
            background: transparent !important;
        }

        /* 表单元素在模态框中的样式 */
        .modal-body .form-control,
        .modal-body .form-select {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            color: var(--text-primary);
        }

        .modal-body .form-control:focus,
        .modal-body .form-select:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--primary);
            color: var(--text-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .modal-body .form-label {
            color: var(--text-primary);
            font-weight: 500;
        }

        .modal-body .form-check-label {
            color: var(--text-secondary);
        }

        /* 关闭按钮样式 */
        .btn-close {
            filter: invert(1);
            opacity: 0.8;
        }

        .btn-close:hover {
            opacity: 1;
        }

        /* 修复模态框中的下拉框选项颜色 */
        .modal-body .form-select option {
            background: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background: var(--primary) !important;
            color: white !important;
        }

        /* 徽章样式在模态框中 */
        .modal-body .badge {
            background: var(--primary) !important;
            color: white;
        }

        .modal-body .badge.bg-secondary {
            background: var(--dark-card) !important;
            color: var(--text-secondary);
        }

        .modal-body .badge.bg-success {
            background: var(--success) !important;
            color: white;
        }

        /* 列表样式 */
        .modal-body .list-unstyled li {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        /* 收藏按钮样式 */
        .favorite-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .favorite-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .favorite-btn.favorited {
            background: var(--danger);
            color: white;
        }

        .favorite-btn.favorited:hover {
            background: #dc2626;
        }

        /* 收藏夹空状态 */
        .empty-favorites {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--text-secondary);
        }

        .empty-favorites i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 收藏夹过滤器 */
        .favorites-filter {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item" id="strategyEditorNav" style="display: none;">
                        <a class="nav-link" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-primary btn-sm" onclick="showCreateStrategy()">
                            <i class="fas fa-plus me-1"></i>创建策略
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-4">
        <!-- 页面标题 -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-store me-3"></i>策略市场
                </h1>
                <p class="page-subtitle">发现、分享和购买优质的量化交易策略，构建您的投资组合</p>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control search-box" id="searchInput"
                               placeholder="搜索策略名称、描述或标签...">
                        <button class="btn btn-primary" onclick="searchStrategies()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select search-box" id="categoryFilter" onchange="filterStrategies()">
                        <option value="">所有分类</option>
                        <option value="trend">趋势跟踪</option>
                        <option value="mean_reversion">均值回归</option>
                        <option value="arbitrage">套利策略</option>
                        <option value="momentum">动量策略</option>
                        <option value="volatility">波动率策略</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select search-box" id="sortBy" onchange="sortStrategies()">
                        <option value="rating">按评分排序</option>
                        <option value="downloads">按下载量排序</option>
                        <option value="created_at">按创建时间排序</option>
                        <option value="price">按价格排序</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary me-2" onclick="showCreateStrategy()">
                        <i class="fas fa-plus me-1"></i>创建策略
                    </button>
                    <button class="btn btn-outline-light me-2" onclick="showMyStrategies()">
                        <i class="fas fa-user me-1"></i>我的策略
                    </button>
                    <button class="btn btn-outline-light" onclick="showFavorites()">
                        <i class="fas fa-heart me-1"></i>收藏夹
                    </button>
                </div>
            </div>
        </div>

        <!-- 策略列表 -->
        <div class="row" id="strategiesContainer">
            <div class="col-12 text-center">
                <i class="fas fa-spinner fa-spin fa-2x text-white"></i>
                <p class="text-white mt-2">加载策略中...</p>
            </div>
        </div>

        <!-- 分页 -->
        <div class="row mt-4">
            <div class="col-12">
                <nav>
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 策略详情模态框 -->
    <div class="modal fade" id="strategyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="strategyTitle">策略详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="strategyContent">
                    <!-- 策略详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-outline-light" id="favoriteBtn" onclick="toggleFavoriteInModal()">
                        <i class="fas fa-heart me-1"></i><span id="favoriteBtnText">添加收藏</span>
                    </button>
                    <button type="button" class="btn btn-warning" id="editBtn" onclick="editStrategy()" style="display: none;">
                        <i class="fas fa-edit me-1"></i>编辑策略
                    </button>
                    <button type="button" class="btn btn-primary" id="downloadBtn">下载策略</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建策略模态框 -->
    <div class="modal fade" id="createStrategyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新策略</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createStrategyForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">策略名称</label>
                                    <input type="text" class="form-control" id="strategyName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <select class="form-select" id="strategyCategory" required>
                                        <option value="">选择分类</option>
                                        <option value="trend">趋势跟踪</option>
                                        <option value="mean_reversion">均值回归</option>
                                        <option value="arbitrage">套利策略</option>
                                        <option value="momentum">动量策略</option>
                                        <option value="volatility">波动率策略</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略描述</label>
                            <textarea class="form-control" id="strategyDescription" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略代码</label>
                            <textarea class="form-control" id="strategyCode" rows="10"
                                      placeholder="# 请输入Python策略代码&#10;def strategy(data):&#10;    # 您的策略逻辑&#10;    return signals" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">标签 (用逗号分隔)</label>
                                    <input type="text" class="form-control" id="strategyTags"
                                           placeholder="例如: 股票, 短线, 技术分析">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isPublic">
                                        <label class="form-check-label" for="isPublic">
                                            公开策略
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isPremium">
                                        <label class="form-check-label" for="isPremium">
                                            付费策略
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="priceSection" style="display: none;">
                            <label class="form-label">价格 (USD)</label>
                            <input type="number" class="form-control" id="strategyPrice" min="0" step="0.01">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createStrategy()">创建策略</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 收藏夹模态框 -->
    <div class="modal fade" id="favoritesModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-heart text-danger me-2"></i>我的收藏夹
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 收藏夹过滤器 -->
                    <div class="favorites-filter">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="favoritesSearch"
                                           placeholder="搜索收藏的策略...">
                                    <button class="btn btn-primary" onclick="searchFavorites()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="favoritesCategory" onchange="filterFavorites()">
                                    <option value="">所有分类</option>
                                    <option value="trend">趋势跟踪</option>
                                    <option value="mean_reversion">均值回归</option>
                                    <option value="arbitrage">套利策略</option>
                                    <option value="momentum">动量策略</option>
                                    <option value="volatility">波动率策略</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="favoritesSortBy" onchange="sortFavorites()">
                                    <option value="date_added">按收藏时间</option>
                                    <option value="rating">按评分排序</option>
                                    <option value="name">按名称排序</option>
                                    <option value="downloads">按下载量</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    共收藏了 <span id="favoritesCount">0</span> 个策略
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- 收藏夹内容 -->
                    <div id="favoritesContainer">
                        <!-- 收藏的策略将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearAllFavorites()">
                        <i class="fas fa-trash me-1"></i>清空收藏夹
                    </button>
                    <button type="button" class="btn btn-primary" onclick="exportFavorites()">
                        <i class="fas fa-download me-1"></i>导出收藏
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 删除重复的注册表单，使用统一登录组件 -->
                    <p class="text-center text-white">
                        <i class="fas fa-info-circle me-2"></i>
                        请使用页面右上角的注册按钮进行注册
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="/static/js/user-auth.js"></script>
    <script>
        let currentPage = 1;
        let strategies = [];
        let favorites = JSON.parse(localStorage.getItem('quanttradex_favorites') || '[]');
        let currentStrategyId = null;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadStrategies();

            // 付费策略复选框事件
            document.getElementById('isPremium').addEventListener('change', function() {
                const priceSection = document.getElementById('priceSection');
                priceSection.style.display = this.checked ? 'block' : 'none';
            });
        });

        // 登录和注册函数已在统一组件中定义，这里删除重复定义

        // 加载策略列表
        async function loadStrategies(page = 1) {
            try {
                const category = document.getElementById('categoryFilter').value;
                const sortBy = document.getElementById('sortBy').value;

                let url = `/api/strategies?page=${page}&per_page=12`;
                if (category) url += `&category=${category}`;
                if (sortBy) url += `&sort_by=${sortBy}`;

                const response = await fetch(url);
                const result = await response.json();

                if (result.success) {
                    strategies = result.strategies;
                    displayStrategies(strategies);
                    updatePagination(result.current_page, result.pages, result.total);
                } else {
                    showError('加载策略失败');
                }
            } catch (error) {
                console.error('加载策略失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示策略列表
        function displayStrategies(strategiesList) {
            const container = document.getElementById('strategiesContainer');

            if (strategiesList.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center">
                        <i class="fas fa-search fa-3x text-white-50 mb-3"></i>
                        <h4 class="text-white">暂无策略</h4>
                        <p class="text-white-50">尝试调整搜索条件或创建新策略</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = strategiesList.map(strategy => `
                <div class="col-md-4 mb-4">
                    <div class="card strategy-card h-100" onclick="showStrategyDetails(${strategy.id})">
                        <button class="favorite-btn ${isFavorited(strategy.id) ? 'favorited' : ''}"
                                onclick="event.stopPropagation(); toggleFavorite(${strategy.id})"
                                title="${isFavorited(strategy.id) ? '取消收藏' : '添加收藏'}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${strategy.name}</h6>
                            ${strategy.is_premium ?
                                `<span class="price-tag">$${strategy.price}</span>` :
                                '<span class="badge bg-success">免费</span>'
                            }
                        </div>
                        <div class="card-body">
                            <p class="card-text small">${strategy.description || '暂无描述'}</p>
                            <div class="mb-2">
                                <span class="category-badge">${getCategoryName(strategy.category)}</span>
                                ${strategy.tags ? strategy.tags.map(tag =>
                                    `<span class="badge bg-secondary ms-1">${tag}</span>`
                                ).join('') : ''}
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="rating">
                                    ${generateStars(strategy.rating)}
                                    <small class="text-white-50">(${strategy.rating.toFixed(1)})</small>
                                </div>
                                <small class="text-white-50">
                                    <i class="fas fa-download me-1"></i>${strategy.downloads}
                                </small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <small class="text-white-50">
                                作者: ${strategy.author} |
                                ${new Date(strategy.created_at).toLocaleDateString()}
                            </small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 生成星级评分
        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            }

            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star"></i>';
            }

            return stars;
        }

        // 获取分类名称
        function getCategoryName(category) {
            const categories = {
                'trend': '趋势跟踪',
                'mean_reversion': '均值回归',
                'arbitrage': '套利策略',
                'momentum': '动量策略',
                'volatility': '波动率策略'
            };
            return categories[category] || category;
        }

        // 显示策略详情
        async function showStrategyDetails(strategyId) {
            try {
                const response = await fetch(`/api/strategies/${strategyId}`);
                const result = await response.json();

                if (result.success) {
                    const strategy = result.strategy;
                    currentStrategyId = strategy.id;
                    document.getElementById('strategyTitle').textContent = strategy.name;
                    document.getElementById('strategyContent').innerHTML = `
                        <div class="row">
                            <div class="col-md-8">
                                <h6>策略描述</h6>
                                <p>${strategy.description || '暂无描述'}</p>

                                <h6>策略代码</h6>
                                <pre><code class="language-python">${strategy.code || '// 代码不可见'}</code></pre>
                            </div>
                            <div class="col-md-4">
                                <h6>策略信息</h6>
                                <ul class="list-unstyled">
                                    <li><strong>分类:</strong> ${getCategoryName(strategy.category)}</li>
                                    <li><strong>作者:</strong> ${strategy.author}</li>
                                    <li><strong>评分:</strong> ${generateStars(strategy.rating)} (${strategy.rating.toFixed(1)})</li>
                                    <li><strong>下载量:</strong> ${strategy.downloads}</li>
                                    <li><strong>价格:</strong> ${strategy.is_premium ? '$' + strategy.price : '免费'}</li>
                                    <li><strong>创建时间:</strong> ${new Date(strategy.created_at).toLocaleDateString()}</li>
                                </ul>

                                ${strategy.tags && strategy.tags.length > 0 ? `
                                    <h6>标签</h6>
                                    <div>
                                        ${strategy.tags.map(tag =>
                                            `<span class="badge bg-secondary me-1">${tag}</span>`
                                        ).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;

                    // 高亮代码
                    Prism.highlightAll();

                    // 更新收藏按钮状态
                    updateFavoriteButtonInModal();

                    // 更新编辑按钮状态
                    updateEditButtonInModal(strategy);

                    new bootstrap.Modal(document.getElementById('strategyModal')).show();
                } else {
                    showError('加载策略详情失败');
                }
            } catch (error) {
                console.error('加载策略详情失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 更新模态框中的编辑按钮
        function updateEditButtonInModal(strategy) {
            const editBtn = document.getElementById('editBtn');

            // 检查用户是否已登录
            const currentUser = getCurrentUser();

            if (currentUser && (currentUser.username === strategy.author || currentUser.is_admin)) {
                // 用户是策略作者或管理员，显示编辑按钮
                editBtn.style.display = 'inline-block';
            } else {
                // 显示复制到编辑器按钮（所有登录用户都可以）
                if (currentUser) {
                    editBtn.style.display = 'inline-block';
                    editBtn.innerHTML = '<i class="fas fa-copy me-1"></i>复制到编辑器';
                    editBtn.className = 'btn btn-info';
                } else {
                    editBtn.style.display = 'none';
                }
            }
        }

        // 获取当前用户信息
        function getCurrentUser() {
            // 从页面导航栏获取用户信息
            const userDropdown = document.querySelector('.dropdown-toggle');
            if (userDropdown && userDropdown.textContent.trim() !== '登录') {
                const username = userDropdown.textContent.trim().replace(/.*\s/, '');
                return {
                    username: username,
                    is_admin: false // 这里可以根据实际情况判断
                };
            }
            return null;
        }

        // 编辑策略或复制到编辑器
        async function editStrategy() {
            if (!currentStrategyId) return;

            try {
                const response = await fetch(`/api/strategies/${currentStrategyId}`);
                const result = await response.json();

                if (result.success) {
                    const strategy = result.strategy;
                    const currentUser = getCurrentUser();

                    if (currentUser && (currentUser.username === strategy.author || currentUser.is_admin)) {
                        // 用户是策略作者或管理员，跳转到编辑页面
                        window.location.href = `/strategy-editor?edit=${strategy.id}`;
                    } else {
                        // 复制策略到编辑器
                        copyStrategyToEditor(strategy);
                    }
                } else {
                    showNotification('获取策略信息失败', 'error');
                }
            } catch (error) {
                console.error('编辑策略失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
            }
        }

        // 复制策略到编辑器
        function copyStrategyToEditor(strategy) {
            // 将策略信息存储到localStorage，供编辑器页面使用
            const strategyTemplate = {
                name: `${strategy.name} (副本)`,
                description: strategy.description,
                code: strategy.code,
                category: strategy.category,
                tags: strategy.tags,
                originalId: strategy.id,
                originalAuthor: strategy.author
            };

            localStorage.setItem('quanttradex_strategy_template', JSON.stringify(strategyTemplate));

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('strategyModal'));
            if (modal) {
                modal.hide();
            }

            // 跳转到策略编辑器
            window.location.href = '/strategy-editor?template=copied';

            showNotification('策略已复制到编辑器，正在跳转...', 'success');
        }

        // 显示创建策略模态框
        function showCreateStrategy() {
            new bootstrap.Modal(document.getElementById('createStrategyModal')).show();
        }

        // 创建策略
        async function createStrategy() {
            const formData = {
                name: document.getElementById('strategyName').value,
                description: document.getElementById('strategyDescription').value,
                code: document.getElementById('strategyCode').value,
                category: document.getElementById('strategyCategory').value,
                tags: document.getElementById('strategyTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                is_public: document.getElementById('isPublic').checked,
                is_premium: document.getElementById('isPremium').checked,
                price: document.getElementById('isPremium').checked ?
                       parseFloat(document.getElementById('strategyPrice').value) || 0 : 0
            };

            try {
                const response = await fetch('/api/strategies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('createStrategyModal')).hide();
                    showSuccess('策略创建成功！');
                    loadStrategies(); // 重新加载策略列表
                } else {
                    showError(result.error || '创建策略失败');
                }
            } catch (error) {
                console.error('创建策略失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 搜索策略
        function searchStrategies() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const filteredStrategies = strategies.filter(strategy =>
                strategy.name.toLowerCase().includes(searchTerm) ||
                (strategy.description && strategy.description.toLowerCase().includes(searchTerm)) ||
                (strategy.tags && strategy.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
            );
            displayStrategies(filteredStrategies);
        }

        // 筛选策略
        function filterStrategies() {
            loadStrategies(1);
        }

        // 排序策略
        function sortStrategies() {
            loadStrategies(1);
        }

        // 更新分页
        function updatePagination(currentPage, totalPages, totalItems) {
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页
            if (currentPage > 1) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadStrategies(${currentPage - 1})">上一页</a>
                    </li>
                `;
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadStrategies(${i})">${i}</a>
                    </li>
                `;
            }

            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadStrategies(${currentPage + 1})">下一页</a>
                    </li>
                `;
            }

            pagination.innerHTML = paginationHTML;
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以实现更好的通知系统
            alert(message);
        }

        // 显示错误消息
        function showError(message) {
            // 这里可以实现更好的通知系统
            alert(message);
        }

        // 登录和注册函数已在统一组件中定义，删除重复定义

        // 显示我的策略
        function showMyStrategies() {
            window.location.href = '/my-strategies';
        }

        // 检查是否已收藏
        function isFavorited(strategyId) {
            return favorites.some(fav => fav.id === strategyId);
        }

        // 保存收藏夹到本地存储
        function saveFavorites() {
            localStorage.setItem('quanttradex_favorites', JSON.stringify(favorites));
        }

        // 切换收藏状态
        function toggleFavorite(strategyId) {
            const strategy = strategies.find(s => s.id === strategyId);
            if (!strategy) return;

            const favoriteIndex = favorites.findIndex(fav => fav.id === strategyId);

            if (favoriteIndex > -1) {
                // 取消收藏
                favorites.splice(favoriteIndex, 1);
                showNotification(`已取消收藏 "${strategy.name}"`, 'info');
            } else {
                // 添加收藏
                const favoriteItem = {
                    ...strategy,
                    dateAdded: new Date().toISOString()
                };
                favorites.push(favoriteItem);
                showNotification(`已收藏 "${strategy.name}"`, 'success');
            }

            saveFavorites();

            // 更新UI
            displayStrategies(strategies);
            updateFavoritesCount();
        }

        // 在模态框中切换收藏状态
        function toggleFavoriteInModal() {
            if (currentStrategyId) {
                toggleFavorite(currentStrategyId);
                updateFavoriteButtonInModal();
            }
        }

        // 更新模态框中的收藏按钮
        function updateFavoriteButtonInModal() {
            const favoriteBtn = document.getElementById('favoriteBtn');
            const favoriteBtnText = document.getElementById('favoriteBtnText');

            if (isFavorited(currentStrategyId)) {
                favoriteBtn.className = 'btn btn-danger';
                favoriteBtnText.textContent = '取消收藏';
            } else {
                favoriteBtn.className = 'btn btn-outline-light';
                favoriteBtnText.textContent = '添加收藏';
            }
        }

        // 更新收藏夹数量显示
        function updateFavoritesCount() {
            const countElement = document.getElementById('favoritesCount');
            if (countElement) {
                countElement.textContent = favorites.length;
            }
        }

        // 显示收藏夹
        function showFavorites() {
            loadFavoritesModal();
            new bootstrap.Modal(document.getElementById('favoritesModal')).show();
        }

        // 加载收藏夹模态框
        function loadFavoritesModal() {
            updateFavoritesCount();
            displayFavorites(favorites);
        }

        // 显示收藏的策略
        function displayFavorites(favoritesList) {
            const container = document.getElementById('favoritesContainer');

            if (favoritesList.length === 0) {
                container.innerHTML = `
                    <div class="empty-favorites">
                        <i class="fas fa-heart-broken"></i>
                        <h4>收藏夹为空</h4>
                        <p>您还没有收藏任何策略，去策略市场看看吧！</p>
                        <button class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="fas fa-store me-1"></i>浏览策略市场
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="row">
                    ${favoritesList.map(strategy => `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card strategy-card h-100" onclick="showStrategyDetails(${strategy.id})">
                                <button class="favorite-btn favorited"
                                        onclick="event.stopPropagation(); removeFavorite(${strategy.id})"
                                        title="取消收藏">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">${strategy.name}</h6>
                                    ${strategy.is_premium ?
                                        `<span class="price-tag">$${strategy.price}</span>` :
                                        '<span class="badge bg-success">免费</span>'
                                    }
                                </div>
                                <div class="card-body">
                                    <p class="card-text small">${strategy.description || '暂无描述'}</p>
                                    <div class="mb-2">
                                        <span class="category-badge">${getCategoryName(strategy.category)}</span>
                                        ${strategy.tags ? strategy.tags.map(tag =>
                                            `<span class="badge bg-secondary ms-1">${tag}</span>`
                                        ).join('') : ''}
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="rating">
                                            ${generateStars(strategy.rating)}
                                            <small class="text-white-50">(${strategy.rating.toFixed(1)})</small>
                                        </div>
                                        <small class="text-white-50">
                                            <i class="fas fa-download me-1"></i>${strategy.downloads}
                                        </small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <small class="text-white-50">
                                        收藏时间: ${new Date(strategy.dateAdded).toLocaleDateString()}
                                    </small>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 从收藏夹中移除策略
        function removeFavorite(strategyId) {
            const strategy = favorites.find(fav => fav.id === strategyId);
            if (!strategy) return;

            const favoriteIndex = favorites.findIndex(fav => fav.id === strategyId);
            if (favoriteIndex > -1) {
                favorites.splice(favoriteIndex, 1);
                saveFavorites();
                showNotification(`已从收藏夹移除 "${strategy.name}"`, 'info');

                // 更新显示
                displayFavorites(favorites);
                updateFavoritesCount();
                displayStrategies(strategies); // 更新主页面的收藏状态
            }
        }

        // 搜索收藏夹
        function searchFavorites() {
            const searchTerm = document.getElementById('favoritesSearch').value.toLowerCase();
            const filteredFavorites = favorites.filter(strategy =>
                strategy.name.toLowerCase().includes(searchTerm) ||
                (strategy.description && strategy.description.toLowerCase().includes(searchTerm)) ||
                (strategy.tags && strategy.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
            );
            displayFavorites(filteredFavorites);
        }

        // 筛选收藏夹
        function filterFavorites() {
            const category = document.getElementById('favoritesCategory').value;
            let filteredFavorites = favorites;

            if (category) {
                filteredFavorites = favorites.filter(strategy => strategy.category === category);
            }

            displayFavorites(filteredFavorites);
        }

        // 排序收藏夹
        function sortFavorites() {
            const sortBy = document.getElementById('favoritesSortBy').value;
            let sortedFavorites = [...favorites];

            switch (sortBy) {
                case 'date_added':
                    sortedFavorites.sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded));
                    break;
                case 'rating':
                    sortedFavorites.sort((a, b) => b.rating - a.rating);
                    break;
                case 'name':
                    sortedFavorites.sort((a, b) => a.name.localeCompare(b.name));
                    break;
                case 'downloads':
                    sortedFavorites.sort((a, b) => b.downloads - a.downloads);
                    break;
            }

            displayFavorites(sortedFavorites);
        }

        // 清空收藏夹
        function clearAllFavorites() {
            if (favorites.length === 0) {
                showNotification('收藏夹已经是空的', 'info');
                return;
            }

            if (confirm(`确定要清空收藏夹吗？这将删除所有 ${favorites.length} 个收藏的策略。`)) {
                favorites = [];
                saveFavorites();
                showNotification('收藏夹已清空', 'success');

                // 更新显示
                displayFavorites(favorites);
                updateFavoritesCount();
                displayStrategies(strategies); // 更新主页面的收藏状态
            }
        }

        // 导出收藏夹
        function exportFavorites() {
            if (favorites.length === 0) {
                showNotification('收藏夹为空，无法导出', 'info');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalCount: favorites.length,
                favorites: favorites.map(fav => ({
                    id: fav.id,
                    name: fav.name,
                    category: fav.category,
                    description: fav.description,
                    rating: fav.rating,
                    downloads: fav.downloads,
                    dateAdded: fav.dateAdded,
                    tags: fav.tags
                }))
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `quanttradex_favorites_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('收藏夹已导出', 'success');
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 删除重复的登录函数，使用统一登录组件

        // 删除重复的注册函数，使用统一登录组件

        // 检查登录状态（已在上面定义，这里删除重复）

        // 更新导航栏用户信息
        function updateNavbarForUser(user) {
            const navbarUser = document.getElementById('navbarUser');
            if (navbarUser && user) {
                // 显示策略开发入口
                const strategyEditorNav = document.getElementById('strategyEditorNav');
                if (strategyEditorNav) strategyEditorNav.style.display = 'block';

                navbarUser.innerHTML = `
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>${user.username}
                            ${user.is_premium ? '<span class="badge bg-warning ms-1">VIP</span>' : ''}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>仪表板</a></li>
                            <li><a class="dropdown-item" href="/strategy-editor"><i class="fas fa-code me-2"></i>策略开发</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showMyStrategies()"><i class="fas fa-list me-2"></i>我的策略</a></li>
                            ${!user.is_premium ? '<li><a class="dropdown-item" href="#" onclick="showUpgrade()"><i class="fas fa-crown me-2"></i>升级VIP</a></li>' : ''}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-primary btn-sm" onclick="showCreateStrategy()">
                            <i class="fas fa-plus me-1"></i>创建策略
                        </button>
                    </li>
                `;
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('已退出登录，正在跳转到首页...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }

        // 登录和注册函数已在统一组件中定义，删除重复定义
    </script>

    <!-- 页脚 -->
    <footer class="text-center py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-secondary mb-3">&copy; 2025 QuantTradeX. 专业量化交易策略平台</p>
                    <div class="d-flex justify-content-center gap-4 flex-wrap mb-3">
                        <a href="/realtime" class="text-secondary text-decoration-none">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                        <a href="/market-data" class="text-secondary text-decoration-none">
                            <i class="fas fa-chart-area me-1"></i>实时行情
                        </a>
                        <a href="/api/system/status" class="text-secondary text-decoration-none">
                            <i class="fas fa-server me-1"></i>系统状态
                        </a>
                        <a href="/api-docs" class="text-secondary text-decoration-none">
                            <i class="fas fa-book me-1"></i>API文档
                        </a>
                        <a href="/contact" class="text-secondary text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>联系我们
                        </a>
                        <a href="/privacy" class="text-secondary text-decoration-none">
                            <i class="fas fa-shield-alt me-1"></i>隐私政策
                        </a>
                        <a href="/terms" class="text-secondary text-decoration-none">
                            <i class="fas fa-file-contract me-1"></i>服务条款
                        </a>
                        <a href="/help" class="text-secondary text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>帮助中心
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>



    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                <div class="modal-header border-bottom border-secondary">
                    <h5 class="modal-title text-white">用户注册</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm" onsubmit="event.preventDefault(); submitRegister();">
                        <div class="mb-3">
                            <label class="form-label text-white">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">全名</label>
                            <input type="text" class="form-control" id="registerFullName"
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-white">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required
                                   style="background: rgba(255,255,255,0.1); border: 1px solid var(--glass-border); color: white;">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
