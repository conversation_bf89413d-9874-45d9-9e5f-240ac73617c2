<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略开发 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --glass-bg: rgba(30, 41, 59, 0.4);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: white;
        }

        .navbar {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            color: white;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary), var(--secondary));
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        /* 修复下拉框选项的字体颜色 */
        .form-select option {
            background: #1e293b;
            color: white;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background: #334155;
        }

        .form-select option:checked {
            background: var(--primary);
            color: white;
        }

        .CodeMirror {
            height: 500px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--glass-border);
        }

        .strategy-template {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .strategy-template:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .parameter-input {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .console-output {
            background: #000000;
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            color: #00ff00;
            line-height: 1.4;
        }

        .tab-content {
            background: transparent;
        }

        .nav-tabs .nav-link {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            color: rgba(255, 255, 255, 0.8);
            margin-right: 5px;
            border-radius: 10px 10px 0 0;
        }

        .nav-tabs .nav-link.active {
            background: var(--glass-bg);
            border-color: var(--glass-border);
            color: white;
        }

        /* 下拉框样式修复 */
        .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* 模态框中的下拉框修复 */
        .modal-body .form-select {
            background-color: rgba(15, 23, 42, 0.8) !important;
            color: var(--text-primary) !important;
        }

        .modal-body .form-select option {
            background-color: var(--dark) !important;
            color: var(--text-primary) !important;
            padding: 8px 12px;
        }

        .modal-body .form-select option:hover {
            background-color: var(--dark-surface) !important;
        }

        .modal-body .form-select option:checked {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* CodeMirror编辑器样式 */
        .CodeMirror {
            height: 600px !important;
            background: #000000 !important;
            color: #ffffff !important;
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            font-size: 16px !important;
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
        }

        .CodeMirror-gutters {
            background: #1a1a1a !important;
            border-right: 1px solid #333 !important;
        }

        .CodeMirror-linenumber {
            color: #666 !important;
        }

        .CodeMirror-cursor {
            border-left: 1px solid #ffffff !important;
        }

        .CodeMirror-selected {
            background: #333333 !important;
        }

        .CodeMirror-line {
            color: #ffffff !important;
        }
        </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>QuantTradeX
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/strategies">
                            <i class="fas fa-store me-1"></i>策略市场
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/strategy-editor">
                            <i class="fas fa-code me-1"></i>策略开发
                        </a>
                    </li>
                    <li class="nav-item" data-feature="stock-prediction" style="display: none;">
                        <a class="nav-link" href="/stock-prediction">
                            <i class="fas fa-brain me-1"></i>AI股票预测
                            <span class="badge bg-warning ms-1">VIP</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/backtest">
                            <i class="fas fa-history me-1"></i>基础回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/advanced_backtest">
                            <i class="fas fa-rocket me-1"></i>高级回测
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/realtime">
                            <i class="fas fa-broadcast-tower me-1"></i>实时数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">
                            <i class="fas fa-comments me-1"></i>社区论坛
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="navbarUser">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showLogin()">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showRegister()">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧：策略模板和参数 -->
            <div class="col-md-3">
                <!-- 策略信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>策略信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">策略名称</label>
                            <input type="text" class="form-control" id="strategyName" placeholder="我的量化策略">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略类型</label>
                            <select class="form-select" id="strategyType">
                                <option value="trend">趋势跟踪</option>
                                <option value="mean_reversion">均值回归</option>
                                <option value="arbitrage">套利策略</option>
                                <option value="momentum">动量策略</option>
                                <option value="ml">机器学习</option>
                                <option value="grid">网格交易</option>
                                <option value="scalping">高频交易</option>
                                <option value="swing">波段交易</option>
                                <option value="pairs">配对交易</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">资产类别</label>
                            <select class="form-select" id="assetClass">
                                <option value="stocks">股票</option>
                                <option value="futures">期货</option>
                                <option value="crypto">数字货币</option>
                                <option value="gold">黄金</option>
                                <option value="forex">外汇</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" id="strategyDescription" rows="3" placeholder="策略描述..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- 策略模板 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code me-2"></i>策略模板
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="strategy-template" onclick="loadTemplate('sma_crossover')">
                            <h6>双均线交叉</h6>
                            <small class="text-muted">经典的移动平均线交叉策略</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('rsi_reversal')">
                            <h6>RSI反转策略</h6>
                            <small class="text-muted">基于RSI指标的反转交易</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('bollinger_bands')">
                            <h6>布林带策略</h6>
                            <small class="text-muted">布林带突破和回归策略</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('macd_strategy')">
                            <h6>MACD策略</h6>
                            <small class="text-muted">MACD金叉死叉交易信号</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('crypto_grid')">
                            <h6>数字货币网格</h6>
                            <small class="text-muted">适用于数字货币的网格交易</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('futures_momentum')">
                            <h6>期货动量策略</h6>
                            <small class="text-muted">期货市场动量追踪策略</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('gold_hedge')">
                            <h6>黄金对冲策略</h6>
                            <small class="text-muted">黄金避险和套利策略</small>
                        </div>
                        <div class="strategy-template" onclick="loadTemplate('forex_carry')">
                            <h6>外汇套息策略</h6>
                            <small class="text-muted">外汇利差交易策略</small>
                        </div>
                    </div>
                </div>

                <!-- 策略参数 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>策略参数
                        </h6>
                    </div>
                    <div class="card-body" id="strategyParameters">
                        <p class="text-muted text-center">选择策略模板后显示参数</p>
                    </div>
                </div>
            </div>

            <!-- 中间：代码编辑器 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-edit me-2"></i>策略代码编辑器
                        </h6>
                        <div>
                            <button class="btn btn-sm btn-outline-light me-2" onclick="formatCode()">
                                <i class="fas fa-code"></i> 格式化
                            </button>
                            <button class="btn btn-sm btn-success me-2" onclick="runStrategy()">
                                <i class="fas fa-play"></i> 运行
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="saveStrategy()">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <textarea id="codeEditor"></textarea>
                    </div>
                </div>

                <!-- 控制台输出 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>控制台输出
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="console-output" id="consoleOutput">
                            <div class="text-muted">等待策略运行...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：帮助和文档 -->
            <div class="col-md-3">
                <!-- 快速帮助 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-question-circle me-2"></i>快速帮助
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#functions">函数</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#indicators">指标</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#examples">示例</a>
                            </li>
                        </ul>
                        <div class="tab-content mt-3">
                            <div class="tab-pane active" id="functions">
                                <div class="small">
                                    <strong>数据获取:</strong><br>
                                    <code>get_price(symbol, period)</code><br>
                                    <code>get_volume(symbol)</code><br><br>
                                    <strong>交易函数:</strong><br>
                                    <code>buy(symbol, quantity)</code><br>
                                    <code>sell(symbol, quantity)</code><br>
                                    <code>close_position(symbol)</code><br><br>
                                    <strong>账户信息:</strong><br>
                                    <code>get_balance()</code><br>
                                    <code>get_positions()</code>
                                </div>
                            </div>
                            <div class="tab-pane" id="indicators">
                                <div class="small">
                                    <strong>技术指标:</strong><br>
                                    <code>sma(data, period)</code><br>
                                    <code>ema(data, period)</code><br>
                                    <code>rsi(data, period)</code><br>
                                    <code>macd(data)</code><br>
                                    <code>bollinger_bands(data, period)</code><br>
                                    <code>stochastic(high, low, close)</code>
                                </div>
                            </div>
                            <div class="tab-pane" id="examples">
                                <div class="small">
                                    <strong>简单买入:</strong><br>
                                    <code>if price > sma_20:<br>&nbsp;&nbsp;buy('AAPL', 100)</code><br><br>
                                    <strong>止损设置:</strong><br>
                                    <code>set_stop_loss('AAPL', 0.05)</code><br><br>
                                    <strong>条件判断:</strong><br>
                                    <code>if rsi < 30:<br>&nbsp;&nbsp;buy_signal = True</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 策略性能 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>策略性能
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="performanceMetrics">
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <p>运行策略后显示性能指标</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入统一登录模态框 -->
    {% include 'components/login_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-auth.js') }}"></script>

    <script>
        let codeEditor;
        let currentTemplate = null;

        // 初始化代码编辑器
        document.addEventListener('DOMContentLoaded', function() {
            // 确保CodeMirror库已加载
            if (typeof CodeMirror === 'undefined') {
                console.error('CodeMirror库未加载');
                return;
            }

            try {
                codeEditor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
                    mode: 'python',
                    theme: 'monokai',
                    lineNumbers: true,
                    autoCloseBrackets: true,
                    matchBrackets: true,
                    indentUnit: 4,
                    indentWithTabs: false,
                    lineWrapping: true,
                    viewportMargin: Infinity,
                    extraKeys: {
                        "Ctrl-Space": "autocomplete",
                        "F11": function(cm) {
                            cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                        },
                        "Esc": function(cm) {
                            if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
                        }
                    }
                });

                // 确保编辑器正确渲染
                setTimeout(() => {
                    codeEditor.refresh();
                }, 100);

                // 检查是否有复制的策略模板
                checkForCopiedStrategy();

            } catch (error) {
                console.error('CodeMirror初始化失败:', error);
                showNotification('代码编辑器初始化失败', 'error');
            }

            // 设置默认代码
            codeEditor.setValue(`# QuantTradeX 策略开发模板
# 这是一个基础的量化交易策略模板

def initialize(context):
    """
    策略初始化函数
    在策略开始运行时调用一次
    """
    # 设置基准和股票池
    context.benchmark = 'SPY'
    context.stocks = ['AAPL', 'GOOGL', 'MSFT', 'AMZN']

    # 设置策略参数
    context.short_window = 20
    context.long_window = 50

    print("策略初始化完成")

def handle_data(context, data):
    """
    主要的策略逻辑函数
    每个交易日都会调用
    """
    for stock in context.stocks:
        # 获取历史价格数据
        prices = data.history(stock, 'close', context.long_window, '1d')

        # 计算移动平均线
        short_ma = prices.tail(context.short_window).mean()
        long_ma = prices.mean()

        # 获取当前持仓
        current_position = context.portfolio.positions[stock].amount

        # 交易逻辑：金叉买入，死叉卖出
        if short_ma > long_ma and current_position == 0:
            # 买入信号
            order_target_percent(stock, 0.25)
            print(f"买入 {stock}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}")

        elif short_ma < long_ma and current_position > 0:
            # 卖出信号
            order_target_percent(stock, 0)
            print(f"卖出 {stock}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}")

def before_trading_start(context, data):
    """
    每个交易日开盘前调用
    """
    pass

def after_trading_end(context, data):
    """
    每个交易日收盘后调用
    """
    # 记录当日组合价值
    record(portfolio_value=context.portfolio.total_value)
`);

            // 检查用户登录状态和会员权限
            checkLoginStatus();
        });

        // 策略模板
        const strategyTemplates = {
            sma_crossover: {
                name: '双均线交叉策略',
                code: `# 双均线交叉策略
def initialize(context):
    context.stocks = ['AAPL', 'GOOGL', 'MSFT']
    context.short_window = 20
    context.long_window = 50

def handle_data(context, data):
    for stock in context.stocks:
        prices = data.history(stock, 'close', context.long_window, '1d')
        short_ma = prices.tail(context.short_window).mean()
        long_ma = prices.mean()

        current_position = context.portfolio.positions[stock].amount

        if short_ma > long_ma and current_position == 0:
            order_target_percent(stock, 0.33)
        elif short_ma < long_ma and current_position > 0:
            order_target_percent(stock, 0)`,
                parameters: [
                    {name: 'short_window', label: '短期窗口', type: 'number', value: 20, min: 5, max: 50},
                    {name: 'long_window', label: '长期窗口', type: 'number', value: 50, min: 20, max: 200},
                    {name: 'position_size', label: '仓位大小', type: 'number', value: 0.33, min: 0.1, max: 1.0, step: 0.01}
                ]
            },
            rsi_reversal: {
                name: 'RSI反转策略',
                code: `# RSI反转策略
import talib

def initialize(context):
    context.stocks = ['AAPL', 'GOOGL', 'MSFT']
    context.rsi_period = 14
    context.rsi_oversold = 30
    context.rsi_overbought = 70

def handle_data(context, data):
    for stock in context.stocks:
        prices = data.history(stock, 'close', context.rsi_period + 10, '1d')
        rsi = talib.RSI(prices.values, timeperiod=context.rsi_period)[-1]

        current_position = context.portfolio.positions[stock].amount

        if rsi < context.rsi_oversold and current_position == 0:
            order_target_percent(stock, 0.33)
        elif rsi > context.rsi_overbought and current_position > 0:
            order_target_percent(stock, 0)`,
                parameters: [
                    {name: 'rsi_period', label: 'RSI周期', type: 'number', value: 14, min: 5, max: 30},
                    {name: 'rsi_oversold', label: '超卖阈值', type: 'number', value: 30, min: 10, max: 40},
                    {name: 'rsi_overbought', label: '超买阈值', type: 'number', value: 70, min: 60, max: 90}
                ]
            },
            bollinger_bands: {
                name: '布林带策略',
                code: `# 布林带策略
import talib

def initialize(context):
    context.stocks = ['AAPL', 'GOOGL', 'MSFT']
    context.bb_period = 20
    context.bb_std = 2

def handle_data(context, data):
    for stock in context.stocks:
        prices = data.history(stock, 'close', context.bb_period + 10, '1d')
        upper, middle, lower = talib.BBANDS(prices.values,
                                          timeperiod=context.bb_period,
                                          nbdevup=context.bb_std,
                                          nbdevdn=context.bb_std)

        current_price = data.current(stock, 'close')
        current_position = context.portfolio.positions[stock].amount

        if current_price < lower[-1] and current_position == 0:
            order_target_percent(stock, 0.33)
        elif current_price > upper[-1] and current_position > 0:
            order_target_percent(stock, 0)`,
                parameters: [
                    {name: 'bb_period', label: '布林带周期', type: 'number', value: 20, min: 10, max: 50},
                    {name: 'bb_std', label: '标准差倍数', type: 'number', value: 2, min: 1, max: 3, step: 0.1}
                ]
            },
            macd_strategy: {
                name: 'MACD策略',
                code: `# MACD策略
import talib

def initialize(context):
    context.stocks = ['AAPL', 'GOOGL', 'MSFT']
    context.macd_fast = 12
    context.macd_slow = 26
    context.macd_signal = 9

def handle_data(context, data):
    for stock in context.stocks:
        prices = data.history(stock, 'close', 50, '1d')
        macd, signal, hist = talib.MACD(prices.values,
                                       fastperiod=context.macd_fast,
                                       slowperiod=context.macd_slow,
                                       signalperiod=context.macd_signal)

        current_position = context.portfolio.positions[stock].amount

        # MACD金叉买入
        if macd[-1] > signal[-1] and macd[-2] <= signal[-2] and current_position == 0:
            order_target_percent(stock, 0.33)
        # MACD死叉卖出
        elif macd[-1] < signal[-1] and macd[-2] >= signal[-2] and current_position > 0:
            order_target_percent(stock, 0)`,
                parameters: [
                    {name: 'macd_fast', label: 'MACD快线', type: 'number', value: 12, min: 5, max: 20},
                    {name: 'macd_slow', label: 'MACD慢线', type: 'number', value: 26, min: 15, max: 40},
                    {name: 'macd_signal', label: '信号线', type: 'number', value: 9, min: 5, max: 15}
                ]
            },
            crypto_grid: {
                name: '数字货币网格策略',
                code: `# 数字货币网格交易策略
def initialize(context):
    context.crypto = 'BTC-USD'
    context.grid_levels = 10
    context.grid_spacing = 0.02  # 2%网格间距
    context.base_amount = 1000   # 基础投资金额
    context.grid_orders = {}

def handle_data(context, data):
    current_price = data.current(context.crypto, 'close')

    # 初始化网格
    if not context.grid_orders:
        setup_grid(context, current_price)

    # 检查网格订单
    check_grid_orders(context, current_price)

def setup_grid(context, base_price):
    """设置网格订单"""
    for i in range(-context.grid_levels//2, context.grid_levels//2 + 1):
        if i == 0:
            continue

        grid_price = base_price * (1 + i * context.grid_spacing)
        order_amount = context.base_amount / context.grid_levels

        if i < 0:  # 买入订单
            context.grid_orders[grid_price] = {'type': 'buy', 'amount': order_amount}
        else:  # 卖出订单
            context.grid_orders[grid_price] = {'type': 'sell', 'amount': order_amount}

def check_grid_orders(context, current_price):
    """检查并执行网格订单"""
    for price, order in list(context.grid_orders.items()):
        if order['type'] == 'buy' and current_price <= price:
            # 执行买入
            order_target_value(context.crypto, order['amount'])
            print(f"网格买入 {context.crypto} at {price}")
            del context.grid_orders[price]

        elif order['type'] == 'sell' and current_price >= price:
            # 执行卖出
            order_target_value(context.crypto, -order['amount'])
            print(f"网格卖出 {context.crypto} at {price}")
            del context.grid_orders[price]`,
                parameters: [
                    {name: 'grid_levels', label: '网格层数', type: 'number', value: 10, min: 5, max: 20},
                    {name: 'grid_spacing', label: '网格间距(%)', type: 'number', value: 2, min: 0.5, max: 5, step: 0.1},
                    {name: 'base_amount', label: '基础金额', type: 'number', value: 1000, min: 100, max: 10000}
                ]
            },
            futures_momentum: {
                name: '期货动量策略',
                code: `# 期货动量追踪策略
def initialize(context):
    context.futures = ['CL=F', 'GC=F', 'ES=F']  # 原油、黄金、标普期货
    context.momentum_period = 20
    context.momentum_threshold = 0.05  # 5%动量阈值
    context.stop_loss = 0.03  # 3%止损

def handle_data(context, data):
    for future in context.futures:
        prices = data.history(future, 'close', context.momentum_period + 1, '1d')

        # 计算动量
        momentum = (prices[-1] / prices[-context.momentum_period] - 1)
        current_position = context.portfolio.positions[future].amount

        # 动量策略逻辑
        if momentum > context.momentum_threshold and current_position == 0:
            # 正动量，买入
            order_target_percent(future, 0.3)
            print(f"动量买入 {future}, 动量: {momentum:.2%}")

        elif momentum < -context.momentum_threshold and current_position == 0:
            # 负动量，卖空
            order_target_percent(future, -0.3)
            print(f"动量卖空 {future}, 动量: {momentum:.2%}")

        elif abs(momentum) < context.momentum_threshold/2 and current_position != 0:
            # 动量减弱，平仓
            order_target_percent(future, 0)
            print(f"平仓 {future}, 动量减弱: {momentum:.2%}")

        # 止损检查
        check_stop_loss(context, data, future)

def check_stop_loss(context, data, symbol):
    """止损检查"""
    position = context.portfolio.positions[symbol]
    if position.amount == 0:
        return

    current_price = data.current(symbol, 'close')
    entry_price = position.cost_basis

    if position.amount > 0:  # 多头止损
        if (entry_price - current_price) / entry_price > context.stop_loss:
            order_target_percent(symbol, 0)
            print(f"多头止损 {symbol}")
    else:  # 空头止损
        if (current_price - entry_price) / entry_price > context.stop_loss:
            order_target_percent(symbol, 0)
            print(f"空头止损 {symbol}")`,
                parameters: [
                    {name: 'momentum_period', label: '动量周期', type: 'number', value: 20, min: 10, max: 50},
                    {name: 'momentum_threshold', label: '动量阈值(%)', type: 'number', value: 5, min: 1, max: 10, step: 0.5},
                    {name: 'stop_loss', label: '止损比例(%)', type: 'number', value: 3, min: 1, max: 10, step: 0.5}
                ]
            },
            gold_hedge: {
                name: '黄金对冲策略',
                code: `# 黄金避险对冲策略
def initialize(context):
    context.gold = 'GLD'  # 黄金ETF
    context.stocks = 'SPY'  # 股票指数ETF
    context.vix = 'VIX'   # 恐慌指数
    context.correlation_period = 60
    context.hedge_threshold = 0.7  # 对冲阈值

def handle_data(context, data):
    # 获取历史数据
    gold_prices = data.history(context.gold, 'close', context.correlation_period, '1d')
    stock_prices = data.history(context.stocks, 'close', context.correlation_period, '1d')

    # 计算相关性
    correlation = gold_prices.corr(stock_prices)

    # 获取VIX水平
    current_vix = data.current(context.vix, 'close')

    # 当前持仓
    gold_position = context.portfolio.positions[context.gold].amount
    stock_position = context.portfolio.positions[context.stocks].amount

    # 对冲策略逻辑
    if current_vix > 25 and correlation < -0.3:  # 高恐慌，负相关
        # 增加黄金配置，减少股票
        if gold_position < 0.4:
            order_target_percent(context.gold, 0.4)
            order_target_percent(context.stocks, 0.6)
            print(f"避险模式：增持黄金，VIX: {current_vix:.1f}")

    elif current_vix < 15 and correlation > 0.3:  # 低恐慌，正相关
        # 减少黄金，增加股票
        if gold_position > 0.1:
            order_target_percent(context.gold, 0.1)
            order_target_percent(context.stocks, 0.9)
            print(f"风险模式：减持黄金，VIX: {current_vix:.1f}")

    # 记录关键指标
    record(vix=current_vix, correlation=correlation,
           gold_weight=gold_position, stock_weight=stock_position)`,
                parameters: [
                    {name: 'correlation_period', label: '相关性周期', type: 'number', value: 60, min: 30, max: 120},
                    {name: 'hedge_threshold', label: '对冲阈值', type: 'number', value: 0.7, min: 0.5, max: 0.9, step: 0.1}
                ]
            },
            forex_carry: {
                name: '外汇套息策略',
                code: `# 外汇套息交易策略
def initialize(context):
    # 高息货币对低息货币
    context.currency_pairs = {
        'AUD/JPY': {'high_yield': 'AUD', 'low_yield': 'JPY'},
        'NZD/JPY': {'high_yield': 'NZD', 'low_yield': 'JPY'},
        'USD/JPY': {'high_yield': 'USD', 'low_yield': 'JPY'}
    }
    context.interest_rates = {
        'AUD': 0.025, 'NZD': 0.02, 'USD': 0.015, 'JPY': -0.001
    }
    context.volatility_threshold = 0.02  # 2%波动率阈值

def handle_data(context, data):
    for pair, currencies in context.currency_pairs.items():
        # 计算利差
        carry = (context.interest_rates[currencies['high_yield']] -
                context.interest_rates[currencies['low_yield']])

        # 获取汇率数据
        prices = data.history(pair, 'close', 30, '1d')
        current_price = data.current(pair, 'close')

        # 计算波动率
        returns = prices.pct_change().dropna()
        volatility = returns.std() * (252 ** 0.5)  # 年化波动率

        current_position = context.portfolio.positions[pair].amount

        # 套息策略逻辑
        if carry > 0.01 and volatility < context.volatility_threshold:
            # 利差足够大且波动率低，建立套息头寸
            if current_position == 0:
                order_target_percent(pair, 0.3)
                print(f"建立套息头寸 {pair}, 利差: {carry:.2%}, 波动率: {volatility:.2%}")

        elif volatility > context.volatility_threshold * 1.5:
            # 波动率过高，平仓
            if current_position != 0:
                order_target_percent(pair, 0)
                print(f"高波动平仓 {pair}, 波动率: {volatility:.2%}")

        # 趋势确认
        sma_short = prices.tail(10).mean()
        sma_long = prices.tail(20).mean()

        if current_position > 0 and sma_short < sma_long:
            # 趋势转向，减仓
            order_target_percent(pair, current_position * 0.5)
            print(f"趋势转向减仓 {pair}")`,
                parameters: [
                    {name: 'volatility_threshold', label: '波动率阈值(%)', type: 'number', value: 2, min: 1, max: 5, step: 0.1},
                    {name: 'carry_threshold', label: '利差阈值(%)', type: 'number', value: 1, min: 0.5, max: 3, step: 0.1}
                ]
            }
        };

        // 加载策略模板
        function loadTemplate(templateName) {
            const template = strategyTemplates[templateName];
            if (template) {
                currentTemplate = templateName;
                codeEditor.setValue(template.code);
                document.getElementById('strategyName').value = template.name;

                // 显示参数配置
                displayParameters(template.parameters);

                showNotification(`已加载模板: ${template.name}`, 'success');
            }
        }

        // 显示策略参数
        function displayParameters(parameters) {
            const container = document.getElementById('strategyParameters');

            if (!parameters || parameters.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">此模板无可配置参数</p>';
                return;
            }

            let html = '';
            parameters.forEach(param => {
                html += `
                    <div class="parameter-input">
                        <label class="form-label small">${param.label}</label>
                        <input type="${param.type}"
                               class="form-control form-control-sm"
                               id="param_${param.name}"
                               value="${param.value}"
                               ${param.min ? `min="${param.min}"` : ''}
                               ${param.max ? `max="${param.max}"` : ''}
                               ${param.step ? `step="${param.step}"` : ''}
                               onchange="updateParameter('${param.name}', this.value)">
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 更新参数
        function updateParameter(paramName, value) {
            // 这里可以实时更新代码中的参数值
            showNotification(`参数 ${paramName} 已更新为 ${value}`, 'info');
        }

        // 格式化代码
        function formatCode() {
            // 简单的Python代码格式化
            const code = codeEditor.getValue();
            // 这里可以集成更复杂的代码格式化工具
            showNotification('代码格式化完成', 'success');
        }

        // 运行策略
        async function runStrategy() {
            const code = codeEditor.getValue();
            const strategyName = document.getElementById('strategyName').value || '未命名策略';

            if (!code.trim()) {
                showNotification('请输入策略代码', 'error');
                return;
            }

            // 显示运行状态
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.innerHTML = `
                <div class="text-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>正在运行策略: ${strategyName}
                </div>
            `;

            try {
                // 模拟策略运行
                setTimeout(() => {
                    const mockResults = generateMockResults();
                    displayRunResults(mockResults);
                    displayPerformanceMetrics(mockResults);
                }, 2000);

            } catch (error) {
                consoleOutput.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>策略运行失败: ${error.message}
                    </div>
                `;
            }
        }

        // 生成模拟运行结果
        function generateMockResults() {
            return {
                total_return: (Math.random() * 30 - 10).toFixed(2), // -10% to 20%
                sharpe_ratio: (Math.random() * 2 + 0.5).toFixed(2), // 0.5 to 2.5
                max_drawdown: -(Math.random() * 15 + 2).toFixed(2), // -2% to -17%
                win_rate: (Math.random() * 40 + 40).toFixed(1), // 40% to 80%
                trades_count: Math.floor(Math.random() * 100 + 20), // 20 to 120
                profit_factor: (Math.random() * 2 + 0.8).toFixed(2), // 0.8 to 2.8
                execution_time: (Math.random() * 5 + 1).toFixed(1) // 1 to 6 seconds
            };
        }

        // 显示运行结果
        function displayRunResults(results) {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.innerHTML = `
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle me-2"></i>策略运行完成 (${results.execution_time}秒)
                </div>
                <div class="small">
                    <div>总收益率: <span class="text-${results.total_return >= 0 ? 'success' : 'danger'}">${results.total_return}%</span></div>
                    <div>夏普比率: ${results.sharpe_ratio}</div>
                    <div>最大回撤: <span class="text-danger">${results.max_drawdown}%</span></div>
                    <div>胜率: ${results.win_rate}%</div>
                    <div>交易次数: ${results.trades_count}</div>
                    <div>盈亏比: ${results.profit_factor}</div>
                </div>
            `;
        }

        // 显示性能指标
        function displayPerformanceMetrics(results) {
            const container = document.getElementById('performanceMetrics');
            container.innerHTML = `
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="small text-muted">总收益</div>
                        <div class="h6 text-${results.total_return >= 0 ? 'success' : 'danger'}">${results.total_return}%</div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="small text-muted">夏普比率</div>
                        <div class="h6">${results.sharpe_ratio}</div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="small text-muted">最大回撤</div>
                        <div class="h6 text-danger">${results.max_drawdown}%</div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="small text-muted">胜率</div>
                        <div class="h6">${results.win_rate}%</div>
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-sm btn-primary" onclick="showDetailedResults()">
                        <i class="fas fa-chart-line me-1"></i>详细分析
                    </button>
                </div>
            `;
        }

        // 保存策略
        async function saveStrategy() {
            // 检查登录状态
            if (!currentUser) {
                showNotification('请先登录以保存策略', 'error');
                showLogin();
                return;
            }

            const strategyData = {
                name: document.getElementById('strategyName').value,
                type: document.getElementById('strategyType').value,
                asset_class: document.getElementById('assetClass').value,
                description: document.getElementById('strategyDescription').value,
                code: codeEditor.getValue(),
                template: currentTemplate
            };

            if (!strategyData.name.trim()) {
                showNotification('请输入策略名称', 'error');
                return;
            }

            if (!strategyData.code.trim()) {
                showNotification('请输入策略代码', 'error');
                return;
            }

            try {
                const response = await fetch('/api/strategies/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(strategyData)
                });

                const data = await response.json();

                if (response.ok) {
                    showNotification('策略保存成功！', 'success');
                } else {
                    if (response.status === 401) {
                        showNotification('登录已过期，请重新登录', 'error');
                        currentUser = null;
                        updateNavbarForGuest();
                        showLogin();
                    } else {
                        showNotification(data.message || '保存失败', 'error');
                    }
                }

            } catch (error) {
                console.error('保存策略错误:', error);
                showNotification('保存失败，请稍后重试', 'error');
            }
        }

        // 显示详细结果
        function showDetailedResults() {
            showNotification('详细分析功能开发中...', 'info');
        }

        // 检查是否有复制的策略模板
        function checkForCopiedStrategy() {
            const urlParams = new URLSearchParams(window.location.search);
            const templateType = urlParams.get('template');

            if (templateType === 'copied') {
                const strategyTemplate = localStorage.getItem('quanttradex_strategy_template');
                if (strategyTemplate) {
                    try {
                        const template = JSON.parse(strategyTemplate);
                        loadCopiedStrategy(template);

                        // 清除localStorage中的模板
                        localStorage.removeItem('quanttradex_strategy_template');

                        // 清除URL参数
                        window.history.replaceState({}, document.title, window.location.pathname);
                    } catch (error) {
                        console.error('解析策略模板失败:', error);
                        showNotification('加载复制的策略失败', 'error');
                    }
                }
            }
        }

        // 加载复制的策略
        function loadCopiedStrategy(template) {
            // 填充策略信息
            document.getElementById('strategyName').value = template.name || '';
            document.getElementById('strategyDescription').value = template.description || '';
            document.getElementById('strategyType').value = template.category || 'trend';
            document.getElementById('assetClass').value = 'stocks';

            // 设置代码编辑器内容
            if (codeEditor && template.code) {
                codeEditor.setValue(template.code);
            }

            // 显示成功消息
            showNotification(`已成功复制策略 "${template.name.replace(' (副本)', '')}" 到编辑器`, 'success');

            // 添加来源信息到描述
            const originalDesc = template.description || '';
            const sourceInfo = `\n\n[复制自: ${template.originalAuthor} 的策略]`;
            document.getElementById('strategyDescription').value = originalDesc + sourceInfo;
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: none;
                border-radius: 10px;
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 用户状态管理
        let currentUser = null;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated) {
                        currentUser = data.user;
                        updateNavbarForLoggedInUser(data.user);
                    } else {
                        updateNavbarForGuest();
                    }
                } else {
                    updateNavbarForGuest();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                updateNavbarForGuest();
            }
        }

        // 更新导航栏为已登录用户
        function updateNavbarForLoggedInUser(user) {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>${user.username}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
                        <li><a class="dropdown-item text-white" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item text-white" href="/my-strategies"><i class="fas fa-code me-2"></i>我的策略</a></li>
                        <li><a class="dropdown-item text-white" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider" style="border-color: var(--glass-border);"></li>
                        <li><a class="dropdown-item text-white" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </li>
            `;
        }

        // 更新导航栏为访客
        function updateNavbarForGuest() {
            const navbarUser = document.getElementById('navbarUser');
            navbarUser.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showLogin()">
                        <i class="fas fa-sign-in-alt me-1"></i>登录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="showRegister()">
                        <i class="fas fa-user-plus me-1"></i>注册
                    </a>
                </li>
            `;
        }

        // 显示登录模态框
        function showLogin() {
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            loginModal.show();
        }

        // 显示注册模态框
        function showRegister() {
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            registerModal.show();
        }

        // 执行登录 - 适配统一登录组件
        async function performLogin() {
            // 尝试从统一登录组件获取字段，如果不存在则从策略编辑器特有字段获取
            const username = document.getElementById('loginUsername')?.value || document.getElementById('loginEmail')?.value;
            const password = document.getElementById('loginPassword').value;
            const rememberMe = document.getElementById('rememberMe')?.checked || false;

            if (!username || !password) {
                showNotification('请填写完整的登录信息', 'error');
                return;
            }

            try {
                // 使用统一的登录API
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,  // 后端会自动识别用户名或邮箱
                        password: password,
                        remember_me: rememberMe
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('登录成功！', 'success');
                    currentUser = result.user;
                    updateNavbarForLoggedInUser(result.user);

                    // 关闭模态框
                    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
                    if (loginModal) {
                        loginModal.hide();
                    }

                    // 动态更新页面用户状态，而不是刷新页面
                    // 策略编辑器页面已经有自己的导航栏更新逻辑，不需要额外处理
                } else {
                    showNotification(result.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showNotification('登录失败，请稍后重试', 'error');
            }
        }

        // 执行注册 - 适配统一登录组件
        async function performRegister() {
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const fullName = document.getElementById('registerFullName')?.value || '';
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword')?.value;

            if (!username || !email || !password) {
                showNotification('请填写完整的注册信息', 'error');
                return;
            }

            if (confirmPassword && password !== confirmPassword) {
                showNotification('两次输入的密码不一致', 'error');
                return;
            }

            try {
                // 使用统一的注册API
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        full_name: fullName,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('注册成功！', 'success');
                    currentUser = result.user;
                    updateNavbarForLoggedInUser(result.user);

                    // 关闭注册模态框
                    const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
                    if (registerModal) {
                        registerModal.hide();
                    }

                    // 注册成功后自动登录，不需要跳转到登录页面
                } else {
                    showNotification(result.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showNotification('注册失败，请稍后重试', 'error');
            }
        }

        // 退出登录
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    currentUser = null;
                    updateNavbarForGuest();
                    showNotification('已退出登录', 'success');
                } else {
                    showNotification('退出登录失败', 'error');
                }
            } catch (error) {
                console.error('退出登录错误:', error);
                showNotification('退出登录失败', 'error');
            }
        }
    </script>
</body>
</html>
